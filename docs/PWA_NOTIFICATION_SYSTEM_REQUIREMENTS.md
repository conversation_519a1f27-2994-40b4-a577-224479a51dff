# PWA and Desktop Dashboard Notification System - Comprehensive Requirements

## Current Infrastructure Assessment

✅ **Already Implemented:**
- Complete PWA manifest configuration (`/public/manifest.json`)
- Advanced service worker with push notification event handlers
- Basic notification settings in user preferences
- Email notification system for maintenance tasks and damage reports
- Real-time capabilities via Supabase Realtime

⚠️ **Needs Implementation:**
- Push notification subscription management
- Real-time notification delivery system
- Desktop notification triggers for task assignments
- Notification preferences UI enhancements

## 1. Database Schema Enhancements

### Notification Management Tables

```sql
-- Push notification subscriptions
CREATE TABLE push_subscriptions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
  team_id uuid REFERENCES teams(id) ON DELETE CASCADE,
  endpoint text NOT NULL,
  p256dh_key text NOT NULL,
  auth_key text NOT NULL,
  user_agent text,
  device_type text, -- 'desktop', 'mobile', 'tablet'
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  UNIQUE(user_id, endpoint)
);

-- Notification queue/history
CREATE TABLE notifications (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
  team_id uuid REFERENCES teams(id) ON DELETE CASCADE,
  type text NOT NULL, -- 'maintenance_assigned', 'damage_assigned', 'task_due', 'task_completed'
  title text NOT NULL,
  body text NOT NULL,
  data jsonb DEFAULT '{}',
  icon text,
  badge text,
  action_url text,
  priority text DEFAULT 'normal', -- 'low', 'normal', 'high', 'urgent'
  is_read boolean DEFAULT false,
  delivery_status text DEFAULT 'pending', -- 'pending', 'sent', 'failed', 'delivered'
  sent_at timestamptz,
  read_at timestamptz,
  expires_at timestamptz,
  created_at timestamptz DEFAULT now()
);

-- Notification preferences (extend existing user_settings)
ALTER TABLE user_settings ADD COLUMN IF NOT EXISTS notification_preferences jsonb DEFAULT '{
  "maintenance_assigned": {"push": true, "email": true, "desktop": true},
  "damage_assigned": {"push": true, "email": true, "desktop": true},
  "task_due": {"push": true, "email": false, "desktop": true},
  "task_completed": {"push": false, "email": true, "desktop": false},
  "inventory_low": {"push": true, "email": true, "desktop": true},
  "quiet_hours": {"enabled": false, "start": "22:00", "end": "08:00"}
}';
```

## 2. Push Notification Service Integration

### Web Push Implementation

```typescript
// services/pushNotificationService.ts
export class PushNotificationService {
  private vapidPublicKey = 'YOUR_VAPID_PUBLIC_KEY';
  
  async requestPermission(): Promise<NotificationPermission> {
    if (!('Notification' in window)) {
      throw new Error('Browser does not support notifications');
    }
    
    return await Notification.requestPermission();
  }
  
  async subscribeToPush(userId: string, teamId: string): Promise<PushSubscription> {
    const registration = await navigator.serviceWorker.ready;
    
    const subscription = await registration.pushManager.subscribe({
      userVisibleOnly: true,
      applicationServerKey: this.vapidPublicKey
    });
    
    // Save subscription to database
    await this.saveSubscription(userId, teamId, subscription);
    
    return subscription;
  }
  
  async saveSubscription(userId: string, teamId: string, subscription: PushSubscription) {
    const { data, error } = await supabase
      .from('push_subscriptions')
      .upsert({
        user_id: userId,
        team_id: teamId,
        endpoint: subscription.endpoint,
        p256dh_key: subscription.toJSON().keys?.p256dh,
        auth_key: subscription.toJSON().keys?.auth,
        user_agent: navigator.userAgent,
        device_type: this.getDeviceType(),
        updated_at: new Date().toISOString()
      });
      
    if (error) throw error;
    return data;
  }
  
  private getDeviceType(): string {
    const userAgent = navigator.userAgent;
    if (/tablet|ipad|playbook|silk/i.test(userAgent)) {
      return 'tablet';
    }
    if (/mobile|iphone|ipod|android|blackberry|opera|mini|windows\sce|palm|smartphone|iemobile/i.test(userAgent)) {
      return 'mobile';
    }
    return 'desktop';
  }
}
```

## 3. Real-time Notification System

### Supabase Realtime Integration

```typescript
// hooks/useRealtimeNotifications.ts
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';

interface Notification {
  id: string;
  user_id: string;
  team_id: string;
  type: string;
  title: string;
  body: string;
  data: any;
  action_url: string;
  priority: string;
  is_read: boolean;
  created_at: string;
}

export const useRealtimeNotifications = () => {
  const { authState } = useAuth();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  
  useEffect(() => {
    if (!authState.user?.id) return;
    
    // Subscribe to new notifications
    const notificationChannel = supabase
      .channel('notifications')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'notifications',
          filter: `user_id=eq.${authState.user.id}`
        },
        (payload) => {
          const newNotification = payload.new as Notification;
          
          // Show desktop notification
          if (newNotification.priority === 'high' || newNotification.priority === 'urgent') {
            showDesktopNotification(newNotification);
          }
          
          // Update local state
          setNotifications(prev => [newNotification, ...prev]);
          
          // Trigger push notification
          triggerPushNotification(newNotification);
        }
      )
      .subscribe();
      
    // Subscribe to maintenance task assignments
    const maintenanceChannel = supabase
      .channel('maintenance_assignments')
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'maintenance_tasks',
          filter: `assigned_to=eq.${authState.user.id}`
        },
        (payload) => {
          handleMaintenanceAssignment(payload.new);
        }
      )
      .subscribe();
      
    return () => {
      notificationChannel.unsubscribe();
      maintenanceChannel.unsubscribe();
    };
  }, [authState.user?.id]);
  
  const showDesktopNotification = (notification: Notification) => {
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification(notification.title, {
        body: notification.body,
        icon: '/icons/icon-192x192.png',
        badge: '/icons/badge-96x96.png',
        tag: notification.id,
        data: notification.data,
        requireInteraction: notification.priority === 'urgent'
      });
    }
  };
  
  const triggerPushNotification = async (notification: Notification) => {
    try {
      await supabase.functions.invoke('send-push-notification', {
        body: {
          user_id: notification.user_id,
          notification: {
            title: notification.title,
            body: notification.body,
            icon: '/icons/icon-192x192.png',
            badge: '/icons/badge-96x96.png',
            data: notification.data,
            actions: [
              { action: 'view', title: 'View Details' },
              { action: 'dismiss', title: 'Dismiss' }
            ]
          }
        }
      });
    } catch (error) {
      console.error('Failed to send push notification:', error);
    }
  };
  
  const handleMaintenanceAssignment = (task: any) => {
    // Handle real-time maintenance task assignment
    console.log('Maintenance task assigned:', task);
  };
  
  return {
    notifications,
    setNotifications
  };
};
```

## 4. Desktop Notification Triggers

### Maintenance Task Assignment Notifications

```typescript
// services/notificationTriggers.ts
import { supabase } from '@/integrations/supabase/client';

interface MaintenanceTask {
  id: string;
  title: string;
  property_name: string;
  severity: string;
  due_date?: string;
  team_id: string;
  assigned_to: string;
}

interface DamageReport {
  id: string;
  title: string;
  property_name: string;
  status: string;
  damage_type?: string;
  team_id: string;
  provider_id: string;
}

export const triggerMaintenanceAssignmentNotification = async (
  assigneeId: string,
  task: MaintenanceTask
) => {
  const notification = {
    user_id: assigneeId,
    team_id: task.team_id,
    type: 'maintenance_assigned',
    title: `New Maintenance Task: ${task.title}`,
    body: `Assigned to you at ${task.property_name}. Due: ${task.due_date || 'No due date'}`,
    data: {
      task_id: task.id,
      property_name: task.property_name,
      severity: task.severity,
      due_date: task.due_date
    },
    action_url: `/maintenance/${task.id}`,
    priority: task.severity === 'high' ? 'high' : 'normal',
    expires_at: task.due_date ? new Date(task.due_date) : null
  };
  
  await createNotification(notification);
};

export const triggerDamageReportAssignmentNotification = async (
  providerId: string,
  report: DamageReport
) => {
  const notification = {
    user_id: providerId,
    team_id: report.team_id,
    type: 'damage_assigned',
    title: `New Damage Report: ${report.title}`,
    body: `Assigned to you at ${report.property_name}. Status: ${report.status}`,
    data: {
      report_id: report.id,
      property_name: report.property_name,
      status: report.status,
      damage_type: report.damage_type
    },
    action_url: `/damages/${report.id}`,
    priority: 'high',
    expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days
  };
  
  await createNotification(notification);
};

const createNotification = async (notification: any) => {
  const { data, error } = await supabase
    .from('notifications')
    .insert(notification);
    
  if (error) {
    console.error('Failed to create notification:', error);
    throw error;
  }
  
  return data;
};
```

## 5. Enhanced Service Worker

### Update existing service-worker.js

```javascript
// Add to existing /public/service-worker.js
const NOTIFICATION_CONFIG = {
  badge: '/icons/badge-96x96.png',
  icon: '/icons/icon-192x192.png',
  vibrate: [200, 100, 200],
  actions: [
    {
      action: 'view',
      title: 'View Details',
      icon: '/icons/view-icon.png'
    },
    {
      action: 'dismiss',
      title: 'Dismiss',
      icon: '/icons/dismiss-icon.png'
    }
  ]
};

// Enhanced push notification listener
self.addEventListener('push', function(event) {
  if (!event.data) return;
  
  const data = event.data.json();
  const options = {
    ...NOTIFICATION_CONFIG,
    body: data.body,
    data: data.data || {},
    tag: data.tag || 'default',
    requireInteraction: data.priority === 'urgent',
    silent: data.priority === 'low'
  };
  
  event.waitUntil(
    self.registration.showNotification(data.title, options)
  );
});

// Enhanced notification click handler
self.addEventListener('notificationclick', function(event) {
  event.notification.close();
  
  if (event.action === 'view') {
    const actionUrl = event.notification.data?.action_url || '/dashboard';
    event.waitUntil(
      clients.openWindow(actionUrl).then(() => {
        // Mark notification as read
        return markNotificationAsRead(event.notification.data?.notification_id);
      })
    );
  } else if (event.action === 'dismiss') {
    // Mark notification as read in database
    event.waitUntil(
      markNotificationAsRead(event.notification.data?.notification_id)
    );
  } else {
    // Default action - open the app
    const actionUrl = event.notification.data?.action_url || '/dashboard';
    event.waitUntil(
      clients.openWindow(actionUrl)
    );
  }
});

// Helper function to mark notification as read
async function markNotificationAsRead(notificationId) {
  if (!notificationId) return;
  
  try {
    // This would need to be implemented as an API call
    // since service workers can't directly access Supabase client
    await fetch('/api/notifications/mark-read', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ notificationId })
    });
  } catch (error) {
    console.error('Failed to mark notification as read:', error);
  }
}
```

## 6. Notification Preferences UI

### Enhanced Settings Component

```typescript
// components/NotificationSettings.tsx
import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { useAuth } from '@/contexts/AuthContext';
import { PushNotificationService } from '@/services/pushNotificationService';

interface NotificationPreferences {
  maintenance_assigned: {
    push: boolean;
    email: boolean;
    desktop: boolean;
  };
  damage_assigned: {
    push: boolean;
    email: boolean;
    desktop: boolean;
  };
  task_due: {
    push: boolean;
    email: boolean;
    desktop: boolean;
  };
  task_completed: {
    push: boolean;
    email: boolean;
    desktop: boolean;
  };
  inventory_low: {
    push: boolean;
    email: boolean;
    desktop: boolean;
  };
  quiet_hours: {
    enabled: boolean;
    start: string;
    end: string;
  };
}

export const NotificationSettings = () => {
  const { authState } = useAuth();
  const [preferences, setPreferences] = useState<NotificationPreferences | null>(null);
  const [pushSubscription, setPushSubscription] = useState<PushSubscription | null>(null);
  const [loading, setLoading] = useState(true);
  
  const notificationTypes = [
    {
      key: 'maintenance_assigned',
      label: 'Maintenance Task Assignments',
      description: 'When you are assigned a new maintenance task'
    },
    {
      key: 'damage_assigned',
      label: 'Damage Report Assignments',
      description: 'When you are assigned a new damage report'
    },
    {
      key: 'task_due',
      label: 'Task Due Reminders',
      description: 'Reminders when tasks are approaching their due date'
    },
    {
      key: 'task_completed',
      label: 'Task Completion Updates',
      description: 'When assigned tasks are marked as completed'
    },
    {
      key: 'inventory_low',
      label: 'Low Inventory Alerts',
      description: 'When inventory items are running low'
    }
  ];
  
  useEffect(() => {
    loadPreferences();
    checkPushSubscription();
  }, [authState.user?.id]);
  
  const loadPreferences = async () => {
    if (!authState.user?.id) return;
    
    try {
      const { data, error } = await supabase
        .from('user_settings')
        .select('notification_preferences')
        .eq('user_id', authState.user.id)
        .single();
        
      if (data?.notification_preferences) {
        setPreferences(data.notification_preferences);
      }
    } catch (error) {
      console.error('Failed to load notification preferences:', error);
    } finally {
      setLoading(false);
    }
  };
  
  const checkPushSubscription = async () => {
    try {
      const registration = await navigator.serviceWorker.ready;
      const subscription = await registration.pushManager.getSubscription();
      setPushSubscription(subscription);
    } catch (error) {
      console.error('Failed to check push subscription:', error);
    }
  };
  
  const handleEnablePushNotifications = async () => {
    if (!authState.user?.id) return;
    
    try {
      const pushService = new PushNotificationService();
      const permission = await pushService.requestPermission();
      
      if (permission === 'granted') {
        const subscription = await pushService.subscribeToPush(
          authState.user.id,
          authState.user.team_id || ''
        );
        setPushSubscription(subscription);
      }
    } catch (error) {
      console.error('Failed to enable push notifications:', error);
    }
  };
  
  const updatePreferences = async (key: string, newPrefs: any) => {
    if (!authState.user?.id || !preferences) return;
    
    const updatedPreferences = {
      ...preferences,
      [key]: newPrefs
    };
    
    try {
      const { error } = await supabase
        .from('user_settings')
        .update({ notification_preferences: updatedPreferences })
        .eq('user_id', authState.user.id);
        
      if (!error) {
        setPreferences(updatedPreferences);
      }
    } catch (error) {
      console.error('Failed to update notification preferences:', error);
    }
  };
  
  if (loading) {
    return <div>Loading notification settings...</div>;
  }
  
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium">Push Notifications</h3>
        <p className="text-sm text-muted-foreground">
          Configure how you receive notifications for different events
        </p>
      </div>
      
      {/* Push notification permission */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium">Browser Notifications</h4>
              <p className="text-sm text-muted-foreground">
                Allow desktop notifications in your browser
              </p>
            </div>
            <Button 
              onClick={handleEnablePushNotifications}
              disabled={!!pushSubscription}
              variant={pushSubscription ? "secondary" : "default"}
            >
              {pushSubscription ? 'Enabled' : 'Enable'}
            </Button>
          </div>
        </CardContent>
      </Card>
      
      {/* Individual notification type settings */}
      {notificationTypes.map(type => (
        <Card key={type.key}>
          <CardContent className="pt-6">
            <div className="space-y-4">
              <div>
                <h4 className="font-medium">{type.label}</h4>
                <p className="text-sm text-muted-foreground">{type.description}</p>
              </div>
              
              <div className="flex space-x-6">
                <div className="flex items-center space-x-2">
                  <Switch
                    checked={preferences?.[type.key as keyof NotificationPreferences]?.push || false}
                    onCheckedChange={(checked) => 
                      updatePreferences(type.key, {
                        ...preferences?.[type.key as keyof NotificationPreferences],
                        push: checked
                      })
                    }
                  />
                  <label className="text-sm">Push</label>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Switch
                    checked={preferences?.[type.key as keyof NotificationPreferences]?.email || false}
                    onCheckedChange={(checked) => 
                      updatePreferences(type.key, {
                        ...preferences?.[type.key as keyof NotificationPreferences],
                        email: checked
                      })
                    }
                  />
                  <label className="text-sm">Email</label>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Switch
                    checked={preferences?.[type.key as keyof NotificationPreferences]?.desktop || false}
                    onCheckedChange={(checked) => 
                      updatePreferences(type.key, {
                        ...preferences?.[type.key as keyof NotificationPreferences],
                        desktop: checked
                      })
                    }
                  />
                  <label className="text-sm">Desktop</label>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
      
      {/* Quiet hours */}
      <Card>
        <CardContent className="pt-6">
          <div className="space-y-4">
            <div>
              <h4 className="font-medium">Quiet Hours</h4>
              <p className="text-sm text-muted-foreground">
                Disable notifications during specified hours
              </p>
            </div>
            
            <div className="flex items-center space-x-2">
              <Switch
                checked={preferences?.quiet_hours?.enabled || false}
                onCheckedChange={(checked) => 
                  updatePreferences('quiet_hours', {
                    ...preferences?.quiet_hours,
                    enabled: checked
                  })
                }
              />
              <label className="text-sm">Enable quiet hours</label>
            </div>
            
            {preferences?.quiet_hours?.enabled && (
              <div className="flex space-x-4">
                <div>
                  <label className="text-sm">Start time</label>
                  <input
                    type="time"
                    value={preferences.quiet_hours.start || '22:00'}
                    onChange={(e) => 
                      updatePreferences('quiet_hours', {
                        ...preferences.quiet_hours,
                        start: e.target.value
                      })
                    }
                    className="block mt-1 rounded border border-gray-300 px-3 py-2"
                  />
                </div>
                
                <div>
                  <label className="text-sm">End time</label>
                  <input
                    type="time"
                    value={preferences.quiet_hours.end || '08:00'}
                    onChange={(e) => 
                      updatePreferences('quiet_hours', {
                        ...preferences.quiet_hours,
                        end: e.target.value
                      })
                    }
                    className="block mt-1 rounded border border-gray-300 px-3 py-2"
                  />
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
```

## 7. Supabase Edge Functions

### Push Notification Function

```typescript
// supabase/functions/send-push-notification/index.ts
import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
    );

    const { user_id, notification } = await req.json();
    
    // Get user's push subscriptions
    const { data: subscriptions, error: fetchError } = await supabaseClient
      .from('push_subscriptions')
      .select('*')
      .eq('user_id', user_id)
      .eq('is_active', true);
    
    if (fetchError) {
      throw fetchError;
    }

    if (!subscriptions || subscriptions.length === 0) {
      return new Response(
        JSON.stringify({ success: false, message: 'No active subscriptions found' }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Import web-push dynamically (since it's a Node.js module)
    const webpush = await import('https://esm.sh/web-push@3.6.6');
    
    const vapidPublicKey = Deno.env.get('VAPID_PUBLIC_KEY');
    const vapidPrivateKey = Deno.env.get('VAPID_PRIVATE_KEY');
    const vapidEmail = Deno.env.get('VAPID_EMAIL');

    if (!vapidPublicKey || !vapidPrivateKey || !vapidEmail) {
      throw new Error('VAPID keys not configured');
    }

    webpush.setVapidDetails(`mailto:${vapidEmail}`, vapidPublicKey, vapidPrivateKey);

    const results = await Promise.allSettled(
      subscriptions.map(async (sub) => {
        const pushSubscription = {
          endpoint: sub.endpoint,
          keys: {
            p256dh: sub.p256dh_key,
            auth: sub.auth_key
          }
        };
        
        return await webpush.sendNotification(
          pushSubscription,
          JSON.stringify(notification)
        );
      })
    );

    const successCount = results.filter(result => result.status === 'fulfilled').length;
    const failureCount = results.filter(result => result.status === 'rejected').length;
    
    return new Response(
      JSON.stringify({ 
        success: true, 
        sent: successCount,
        failed: failureCount,
        results 
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  } catch (error) {
    console.error('Push notification error:', error);
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});
```

### Mark Notification as Read Function

```typescript
// supabase/functions/mark-notification-read/index.ts
import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
    );

    const { notification_id } = await req.json();
    
    if (!notification_id) {
      return new Response(
        JSON.stringify({ error: 'notification_id is required' }),
        { 
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    const { data, error } = await supabaseClient
      .from('notifications')
      .update({ 
        is_read: true, 
        read_at: new Date().toISOString() 
      })
      .eq('id', notification_id);
    
    if (error) {
      throw error;
    }
    
    return new Response(
      JSON.stringify({ success: true, data }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  } catch (error) {
    console.error('Mark notification read error:', error);
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});
```

## 8. Database Triggers for Auto-Notifications

```sql
-- Trigger for maintenance task assignments
CREATE OR REPLACE FUNCTION notify_maintenance_assignment()
RETURNS TRIGGER AS $$
DECLARE
  assigned_user_record RECORD;
BEGIN
  -- Only trigger if assigned_to changed from null or different value
  IF (OLD.assigned_to IS DISTINCT FROM NEW.assigned_to) AND NEW.assigned_to IS NOT NULL THEN
    
    -- Get the assigned user's information
    SELECT * INTO assigned_user_record 
    FROM profiles 
    WHERE id = NEW.assigned_to;
    
    -- Insert notification
    INSERT INTO notifications (
      user_id,
      team_id,
      type,
      title,
      body,
      data,
      action_url,
      priority
    ) VALUES (
      NEW.assigned_to,
      NEW.team_id,
      'maintenance_assigned',
      'New Maintenance Task: ' || NEW.title,
      'Assigned to you at ' || COALESCE(NEW.property_name, 'Unknown Property') || 
      CASE 
        WHEN NEW.due_date IS NOT NULL THEN '. Due: ' || NEW.due_date::text
        ELSE ''
      END,
      jsonb_build_object(
        'task_id', NEW.id,
        'property_name', NEW.property_name,
        'severity', NEW.severity,
        'due_date', NEW.due_date,
        'assigned_by', NEW.user_id
      ),
      '/maintenance/' || NEW.id,
      CASE 
        WHEN NEW.severity = 'high' THEN 'high' 
        WHEN NEW.severity = 'urgent' THEN 'urgent'
        ELSE 'normal' 
      END
    );
    
    -- Log the notification creation
    RAISE NOTICE 'Created maintenance assignment notification for user % (task: %)', 
      NEW.assigned_to, NEW.title;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER maintenance_assignment_notification
  AFTER UPDATE ON maintenance_tasks
  FOR EACH ROW
  EXECUTE FUNCTION notify_maintenance_assignment();

-- Trigger for damage report assignments
CREATE OR REPLACE FUNCTION notify_damage_assignment()
RETURNS TRIGGER AS $$
DECLARE
  provider_record RECORD;
BEGIN
  -- Only trigger if provider_id changed from null or different value
  IF (OLD.provider_id IS DISTINCT FROM NEW.provider_id) AND NEW.provider_id IS NOT NULL THEN
    
    -- Get the provider's information
    SELECT * INTO provider_record 
    FROM maintenance_providers 
    WHERE id = NEW.provider_id;
    
    -- Insert notification
    INSERT INTO notifications (
      user_id,
      team_id,
      type,
      title,
      body,
      data,
      action_url,
      priority
    ) VALUES (
      NEW.provider_id,
      NEW.team_id,
      'damage_assigned',
      'New Damage Report: ' || NEW.title,
      'Assigned to you at ' || COALESCE(NEW.property_name, 'Unknown Property') || 
      '. Status: ' || COALESCE(NEW.status, 'Open'),
      jsonb_build_object(
        'report_id', NEW.id,
        'property_name', NEW.property_name,
        'status', NEW.status,
        'damage_type', NEW.damage_type,
        'assigned_by', NEW.user_id
      ),
      '/damages/' || NEW.id,
      'high'
    );
    
    -- Log the notification creation
    RAISE NOTICE 'Created damage assignment notification for provider % (report: %)', 
      NEW.provider_id, NEW.title;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER damage_assignment_notification
  AFTER UPDATE ON damage_reports
  FOR EACH ROW
  EXECUTE FUNCTION notify_damage_assignment();

-- Trigger for task due date reminders
CREATE OR REPLACE FUNCTION notify_task_due_soon()
RETURNS void AS $$
DECLARE
  task_record RECORD;
BEGIN
  -- Find tasks due within 24 hours that haven't been completed
  FOR task_record IN
    SELECT mt.*, p.email as assignee_email
    FROM maintenance_tasks mt
    LEFT JOIN profiles p ON mt.assigned_to = p.id
    WHERE mt.due_date IS NOT NULL
      AND mt.due_date::date = CURRENT_DATE + INTERVAL '1 day'
      AND mt.status NOT IN ('completed', 'cancelled')
      AND mt.assigned_to IS NOT NULL
      -- Avoid duplicate notifications
      AND NOT EXISTS (
        SELECT 1 FROM notifications n 
        WHERE n.type = 'task_due' 
          AND n.data->>'task_id' = mt.id::text
          AND n.created_at::date = CURRENT_DATE
      )
  LOOP
    INSERT INTO notifications (
      user_id,
      team_id,
      type,
      title,
      body,
      data,
      action_url,
      priority
    ) VALUES (
      task_record.assigned_to,
      task_record.team_id,
      'task_due',
      'Task Due Tomorrow: ' || task_record.title,
      'Your task at ' || COALESCE(task_record.property_name, 'Unknown Property') || 
      ' is due tomorrow (' || task_record.due_date::text || ')',
      jsonb_build_object(
        'task_id', task_record.id,
        'property_name', task_record.property_name,
        'due_date', task_record.due_date,
        'severity', task_record.severity
      ),
      '/maintenance/' || task_record.id,
      'normal'
    );
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Create a scheduled function to run daily for due date reminders
-- This would need to be set up as a cron job or scheduled function
```

## 9. API Routes for Notification Management

### Mark Notification as Read API

```typescript
// src/pages/api/notifications/mark-read.ts
import { NextApiRequest, NextApiResponse } from 'next';
import { supabase } from '@/integrations/supabase/client';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { notificationId } = req.body;

    if (!notificationId) {
      return res.status(400).json({ error: 'notificationId is required' });
    }

    const { data, error } = await supabase
      .from('notifications')
      .update({ 
        is_read: true, 
        read_at: new Date().toISOString() 
      })
      .eq('id', notificationId);

    if (error) {
      throw error;
    }

    res.status(200).json({ success: true, data });
  } catch (error) {
    console.error('Mark notification read error:', error);
    res.status(500).json({ error: error.message });
  }
}
```

## 10. Implementation Timeline

### Phase 1: Core Infrastructure (Week 1-2)
- ✅ PWA manifest (already complete)
- ✅ Service worker enhancements (basic structure exists)
- 🔄 Database schema creation
- 🔄 Push notification service setup
- 🔄 VAPID key generation and configuration

### Phase 2: Notification Triggers (Week 3)
- 🔄 Database triggers for automatic notifications
- 🔄 Integration with existing assignment flows
- 🔄 Real-time notification delivery
- 🔄 Desktop notification display

### Phase 3: User Interface (Week 4)
- 🔄 Enhanced notification settings UI
- 🔄 Notification history/inbox
- 🔄 Permission request flows
- 🔄 Quiet hours and preferences

### Phase 4: Testing & Optimization (Week 5)
- 🔄 Cross-browser testing
- 🔄 Performance optimization
- 🔄 Error handling and fallbacks
- 🔄 Analytics and monitoring

## 11. Security Considerations

- **VAPID Keys**: Securely store VAPID public/private keys in environment variables
- **Subscription Security**: Validate subscription endpoints and keys
- **Rate Limiting**: Implement notification rate limits to prevent spam
- **Data Privacy**: Ensure notification data complies with privacy policies
- **Permission Management**: Respect user notification preferences and browser permissions
- **Authentication**: Ensure only authenticated users can manage their notifications
- **Team Isolation**: Ensure notifications are properly scoped to team boundaries

## 12. Testing Strategy

### Unit Tests
- Test notification trigger functions
- Test push notification service methods
- Test notification preference updates

### Integration Tests
- Test end-to-end notification flow
- Test real-time notification delivery
- Test email/push notification coordination

### Browser Testing
- Test across Chrome, Firefox, Safari, Edge
- Test notification permission requests
- Test notification display and interaction

### Mobile Testing
- Test PWA notifications on mobile devices
- Test notification behavior when app is installed
- Test offline notification queuing

### Performance Testing
- Test with high notification volumes
- Test real-time subscription performance
- Test notification cleanup and archival

## 13. Monitoring and Analytics

### Notification Metrics
- Delivery success rates
- User engagement with notifications
- Permission grant/denial rates
- Notification open rates

### Performance Metrics
- Real-time subscription latency
- Push notification delivery time
- Database trigger performance
- Service worker cache efficiency

### Error Tracking
- Failed notification deliveries
- Push subscription errors
- Real-time connection issues
- Service worker errors

## 14. Maintenance and Operations

### Daily Tasks
- Monitor notification delivery rates
- Check for failed push subscriptions
- Review notification performance metrics

### Weekly Tasks
- Clean up old notifications (archive after 30 days)
- Review user notification preferences
- Update VAPID keys if necessary

### Monthly Tasks
- Analyze notification engagement metrics
- Optimize notification content and timing
- Review and update notification types

This comprehensive system will provide seamless desktop and mobile notifications for maintenance task assignments, damage report assignments, and other critical events while maintaining excellent user experience and following PWA best practices.
