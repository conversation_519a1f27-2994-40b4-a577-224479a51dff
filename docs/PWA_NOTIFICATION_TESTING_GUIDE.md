# PWA Notification System Testing Guide

## Overview

This guide provides step-by-step instructions for testing the PWA notification system in the StayFu application. Follow these procedures to validate all notification features work correctly.

## Prerequisites

### Environment Setup
1. **Development Server Running**: `npm run dev`
2. **Supabase Connection**: Ensure database connection is active
3. **HTTPS/Localhost**: Push notifications require secure context
4. **Modern Browser**: Chrome 90+, Firefox 88+, Safari 14+, or Edge 90+

### Test User Setup
1. Create test user accounts with different roles:
   - Admin user
   - Property manager
   - Service provider
2. Ensure users are assigned to the same team
3. Create test properties and maintenance tasks

## Test Scenarios

### 1. Notification Settings Configuration

#### Test 1.1: Access Notification Settings
1. **Navigate**: Go to Settings > Notifications
2. **Expected**: See comprehensive notification settings page
3. **Verify**: 
   - Push notification subscription section
   - Granular notification preferences
   - Quiet hours configuration
   - Test notification button

#### Test 1.2: Browser Support Detection
1. **Test in supported browser** (Chrome/Firefox):
   - Should show "Enable Push Notifications" button
   - Should display device type (Desktop/Mobile)
2. **Test in unsupported browser** (if any):
   - Should show browser compatibility warning
   - Push notification options should be disabled

#### Test 1.3: Push Notification Subscription
1. **Click**: "Enable Push Notifications" button
2. **Expected**: Browser permission prompt appears
3. **Grant Permission**: Click "Allow" in browser prompt
4. **Verify**:
   - Button changes to "Push Notifications Enabled"
   - Green checkmark appears
   - Subscription stored in database (`push_subscriptions` table)

#### Test 1.4: Test Notification
1. **Prerequisites**: Push notifications enabled
2. **Click**: "Send Test Notification" button
3. **Expected**: 
   - Desktop notification appears
   - Notification bell shows unread count
   - Test notification appears in notification list

### 2. Real-time Notification System

#### Test 2.1: Maintenance Assignment Notification
1. **Setup**: Two browser windows/tabs with different users
2. **Action**: User A assigns maintenance task to User B
3. **Expected in User B's browser**:
   - Real-time notification appears in notification bell
   - Desktop notification displays (if enabled)
   - Unread count badge updates
   - Notification includes task details and property name

#### Test 2.2: Damage Report Assignment
1. **Setup**: Admin user and service provider accounts
2. **Action**: Admin assigns damage report to service provider
3. **Expected**:
   - Service provider receives real-time notification
   - Notification includes damage report details
   - Priority level correctly displayed

#### Test 2.3: Task Completion Notification
1. **Setup**: Assigned maintenance task
2. **Action**: Service provider marks task as completed
3. **Expected**:
   - Team managers receive completion notification
   - Notification includes completion details
   - Assignee does not receive their own completion notification

### 3. Notification Bell Interface

#### Test 3.1: Notification Bell Display
1. **With no notifications**:
   - Bell icon appears normal (not highlighted)
   - No badge count visible
2. **With unread notifications**:
   - Bell icon highlighted (BellRing icon)
   - Red badge shows unread count
   - Badge shows "99+" for counts over 99

#### Test 3.2: Notification Dropdown
1. **Click**: Notification bell
2. **Expected**:
   - Dropdown opens with notification list
   - Recent notifications displayed with icons
   - "Mark all read" button visible if unread notifications exist
   - "View all notifications" link at bottom

#### Test 3.3: Notification Actions
1. **Click notification in dropdown**:
   - Notification marked as read
   - Navigates to relevant page (if action_url exists)
   - Dropdown closes
2. **Click "Mark all read"**:
   - All notifications marked as read
   - Unread count resets to 0
   - Bell icon returns to normal state

### 4. Notifications Page

#### Test 4.1: Navigation to Notifications Page
1. **Method 1**: Click "View all notifications" in bell dropdown
2. **Method 2**: Navigate directly to `/notifications`
3. **Expected**: Full notifications page loads with all notifications

#### Test 4.2: Notification Filtering
1. **Test filters**:
   - All Notifications: Shows all notifications
   - Unread Only: Shows only unread notifications
   - By Type: Filter by maintenance, damage, etc.
2. **Expected**: List updates based on selected filter

#### Test 4.3: Notification Management
1. **Individual actions**:
   - Mark single notification as read
   - Delete individual notification
   - Click notification to navigate to details
2. **Batch actions**:
   - Mark all notifications as read
   - Filter and perform actions on subset

### 5. Database Trigger Testing

#### Test 5.1: Automatic Notification Creation
1. **Create maintenance task with assignee**:
   - Check `notifications` table for new entry
   - Verify notification data includes task details
2. **Update task assignment**:
   - Change assignee to different user
   - Verify new notification created for new assignee
3. **Complete task**:
   - Mark task as completed
   - Verify completion notification created for team managers

#### Test 5.2: Due Date Reminders
1. **Create task with due date in 24 hours**:
   - Run `create_due_date_reminders()` function manually
   - Verify reminder notification created
2. **Test duplicate prevention**:
   - Run function again
   - Verify no duplicate reminders created

### 6. Cross-Browser Testing

#### Test 6.1: Chrome/Chromium
- [ ] Push notification subscription works
- [ ] Desktop notifications display correctly
- [ ] Real-time updates function properly
- [ ] Service worker registers successfully

#### Test 6.2: Firefox
- [ ] Push notification subscription works
- [ ] Desktop notifications display correctly
- [ ] Real-time updates function properly
- [ ] Service worker registers successfully

#### Test 6.3: Safari
- [ ] Push notification subscription works (iOS 16.4+)
- [ ] Desktop notifications display correctly
- [ ] Real-time updates function properly
- [ ] Service worker registers successfully

#### Test 6.4: Mobile Browsers
- [ ] Responsive design works on mobile
- [ ] Touch interactions work properly
- [ ] Push notifications work on mobile
- [ ] Performance acceptable on mobile networks

### 7. Performance Testing

#### Test 7.1: Real-time Connection Stability
1. **Long-running session test**:
   - Keep application open for extended period
   - Verify real-time connection remains stable
   - Check for memory leaks in browser dev tools

#### Test 7.2: High Volume Notifications
1. **Create multiple notifications rapidly**:
   - Assign multiple tasks quickly
   - Verify all notifications appear
   - Check performance impact on UI

#### Test 7.3: Network Conditions
1. **Test on slow network**:
   - Throttle network in browser dev tools
   - Verify notifications still function
   - Check for appropriate loading states

### 8. Error Handling Testing

#### Test 8.1: Permission Denied
1. **Deny browser notification permission**:
   - Verify graceful handling
   - Appropriate error message displayed
   - Fallback to in-app notifications only

#### Test 8.2: Network Disconnection
1. **Disconnect network while using app**:
   - Verify offline behavior
   - Check notification queue when reconnected
   - Verify service worker handles offline state

#### Test 8.3: Database Connection Issues
1. **Simulate database connectivity issues**:
   - Verify error handling in notification hooks
   - Check for appropriate user feedback
   - Verify recovery when connection restored

## Test Checklist

### Basic Functionality
- [ ] Notification settings page loads correctly
- [ ] Push notification subscription works
- [ ] Test notification sends successfully
- [ ] Real-time notifications appear
- [ ] Notification bell updates correctly
- [ ] Notifications page displays all notifications

### Advanced Features
- [ ] Notification filtering works
- [ ] Batch operations function correctly
- [ ] Database triggers create notifications automatically
- [ ] Due date reminders work
- [ ] Quiet hours respected
- [ ] Priority levels displayed correctly

### Cross-Browser Compatibility
- [ ] Chrome desktop and mobile
- [ ] Firefox desktop and mobile
- [ ] Safari desktop and mobile
- [ ] Edge desktop and mobile

### Performance and Reliability
- [ ] Real-time connection stable
- [ ] No memory leaks detected
- [ ] Good performance on mobile
- [ ] Graceful error handling

## Troubleshooting Common Issues

### Push Notifications Not Working
1. Check browser console for errors
2. Verify VAPID keys are configured
3. Ensure HTTPS or localhost environment
4. Check browser notification permissions

### Real-time Updates Not Appearing
1. Verify Supabase Realtime connection
2. Check RLS policies in database
3. Ensure user has proper permissions
4. Check browser console for WebSocket errors

### Database Triggers Not Firing
1. Verify trigger functions installed correctly
2. Check PostgreSQL logs for errors
3. Ensure required table columns exist
4. Test trigger functions manually

## Reporting Issues

When reporting issues, include:
1. Browser and version
2. Operating system
3. Steps to reproduce
4. Expected vs actual behavior
5. Console errors (if any)
6. Network conditions
7. User role and permissions

## Success Criteria

The PWA notification system passes testing when:
- ✅ All basic functionality tests pass
- ✅ Cross-browser compatibility confirmed
- ✅ Performance meets acceptable standards
- ✅ Error handling works gracefully
- ✅ Real-time updates function reliably
- ✅ Database triggers work automatically
- ✅ User experience is intuitive and responsive
