-- Debug script to check user <NAME_EMAIL>
-- Run this in Supabase SQL Editor to see what permissions the user has

-- Get user profile info
SELECT 
  'User Profile' as section,
  p.id,
  p.email,
  p.role,
  p.is_super_admin,
  p.created_at
FROM profiles p
WHERE p.email = '<EMAIL>';

-- Get team memberships
SELECT 
  'Team Memberships' as section,
  tm.team_id,
  t.name as team_name,
  tm.role as team_role,
  tm.status,
  tm.created_at
FROM team_members tm
JOIN teams t ON tm.team_id = t.id
JOIN profiles p ON tm.user_id = p.id
WHERE p.email = '<EMAIL>';

-- Get user permissions
SELECT 
  'User Permissions' as section,
  up.team_id,
  t.name as team_name,
  up.permission,
  up.enabled,
  up.created_at
FROM user_permissions up
JOIN teams t ON up.team_id = t.id
JOIN profiles p ON up.user_id = p.id
WHERE p.email = '<EMAIL>'
ORDER BY up.team_id, up.permission;

-- Get properties the user has access to
SELECT 
  'Accessible Properties' as section,
  p.id as property_id,
  p.name as property_name,
  p.address,
  CASE 
    WHEN p.user_id = prof.id THEN 'Owner'
    WHEN tp.team_id IS NOT NULL THEN 'Team Access'
    ELSE 'No Access'
  END as access_type,
  tp.team_id,
  t.name as team_name
FROM properties p
LEFT JOIN profiles prof ON p.user_id = prof.id AND prof.email = '<EMAIL>'
LEFT JOIN team_properties tp ON p.id = tp.property_id
LEFT JOIN teams t ON tp.team_id = t.id
LEFT JOIN team_members tm ON t.id = tm.team_id AND tm.user_id = prof.id AND tm.status = 'active'
WHERE prof.id IS NOT NULL OR tm.user_id IS NOT NULL
ORDER BY p.name;

-- Test the can_create_maintenance_task function for a specific property
-- Replace 'ca7fd3fb-4575-48e5-a6de-f5cdcd156eab' with the actual property ID being used
SELECT 
  'Function Test' as section,
  can_create_maintenance_task('ca7fd3fb-4575-48e5-a6de-f5cdcd156eab'::uuid) as can_create_task,
  'ca7fd3fb-4575-48e5-a6de-f5cdcd156eab' as property_id_tested;
