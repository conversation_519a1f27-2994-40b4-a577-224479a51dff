const { createClient } = require('@supabase/supabase-js');

// Comprehensive AI functionality test script
const SUPABASE_URL = 'https://pwaeknalhosfwuxkpaet.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB3YWVrbmFsaG9zZnd1eGtwYWV0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDEwMjk1MzMsImV4cCI6MjA1NjYwNTUzM30.buZMBA9gPIkvp3rIGJl-QeYIO2IXUC8ea_N-AvLJID4';

const testCommands = [
  // Property Management
  {
    category: "Property Management",
    command: "Add a property named Test Beach House at 456 Ocean Drive, Miami, FL with 3 bedrooms and 2 bathrooms",
    expectedAction: "addProperty"
  },
  
  // Collection Management
  {
    category: "Collection Management", 
    command: "Create a collection named Bathroom Essentials",
    expectedAction: "createCollection"
  },
  
  // Maintenance Tasks
  {
    category: "Maintenance Tasks",
    command: "Create a maintenance task to fix the broken sink at Thames",
    expectedAction: "addMaintenanceTask"
  },
  {
    category: "Maintenance Tasks",
    command: "Fix the door handle, assign to <PERSON>",
    expectedAction: "addMaintenanceTask"
  },
  {
    category: "Maintenance Tasks",
    command: "Schedule HVAC maintenance for next week at Test Beach House, make it high priority",
    expectedAction: "addMaintenanceTask"
  },
  
  // Inventory Management
  {
    category: "Inventory Management",
    command: "Add 10 bath towels to Thames",
    expectedAction: "addInventoryItem"
  },
  {
    category: "Inventory Management",
    command: "Update towel quantity to 15 at Thames",
    expectedAction: "updateInventoryItem"
  },
  {
    category: "Inventory Management",
    command: "Add 5 toilet paper rolls to Test Beach House",
    expectedAction: "addInventoryItem"
  },
  
  // Purchase Orders
  {
    category: "Purchase Orders",
    command: "Create a purchase order for all low stock items",
    expectedAction: "createPurchaseOrder"
  },
  {
    category: "Purchase Orders",
    command: "Order cleaning supplies for Thames",
    expectedAction: "createPurchaseOrder"
  },
  {
    category: "Purchase Orders",
    command: "Create a PO for bathroom supplies",
    expectedAction: "createPurchaseOrder"
  }
];

async function testAIFunctionality() {
  console.log('🧪 Starting Comprehensive AI Functionality Test');
  console.log('=' .repeat(60));
  
  const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
  
  try {
    // Login first
    console.log('1. Logging in...');
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'Newsig1!!!'
    });
    
    if (authError) {
      console.error('❌ Login failed:', authError.message);
      return;
    }
    
    console.log('✅ Login successful, user ID:', authData.user.id);
    console.log('');
    
    // Test each command
    let successCount = 0;
    let failureCount = 0;
    const results = [];
    
    for (let i = 0; i < testCommands.length; i++) {
      const test = testCommands[i];
      console.log(`${i + 1}. Testing: ${test.category}`);
      console.log(`Command: "${test.command}"`);
      console.log(`Expected: ${test.expectedAction}`);
      
      try {
        const { data, error } = await supabase.functions.invoke('ai-command-processor', {
          body: {
            command: test.command,
            originalCommand: test.command,
            hasContext: false
          }
        });
        
        if (error) {
          console.log(`❌ Failed: ${error.message}`);
          failureCount++;
          results.push({
            ...test,
            status: 'failed',
            error: error.message
          });
        } else if (data.success) {
          console.log(`✅ Success: ${data.message}`);
          if (data.action) {
            console.log(`   Action: ${data.action}`);
          }
          if (data.entityId) {
            console.log(`   Entity ID: ${data.entityId}`);
          }
          successCount++;
          results.push({
            ...test,
            status: 'success',
            response: data
          });
        } else {
          console.log(`⚠️  AI Response: ${data.message}`);
          if (data.suggestions && data.suggestions.length > 0) {
            console.log(`   Suggestions: ${data.suggestions[0]}`);
          }
          // Count as partial success if AI responded intelligently
          successCount++;
          results.push({
            ...test,
            status: 'partial',
            response: data
          });
        }
        
      } catch (cmdError) {
        console.log(`❌ Exception: ${cmdError.message}`);
        failureCount++;
        results.push({
          ...test,
          status: 'exception',
          error: cmdError.message
        });
      }
      
      console.log('');
      
      // Add a small delay between tests
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    // Summary
    console.log('=' .repeat(60));
    console.log('📊 TEST SUMMARY');
    console.log('=' .repeat(60));
    console.log(`Total Tests: ${testCommands.length}`);
    console.log(`✅ Successful: ${successCount}`);
    console.log(`❌ Failed: ${failureCount}`);
    console.log(`Success Rate: ${((successCount / testCommands.length) * 100).toFixed(1)}%`);
    console.log('');
    
    // Category breakdown
    const categories = {};
    results.forEach(result => {
      if (!categories[result.category]) {
        categories[result.category] = { success: 0, total: 0 };
      }
      categories[result.category].total++;
      if (result.status === 'success' || result.status === 'partial') {
        categories[result.category].success++;
      }
    });
    
    console.log('📈 RESULTS BY CATEGORY:');
    Object.entries(categories).forEach(([category, stats]) => {
      const rate = ((stats.success / stats.total) * 100).toFixed(1);
      console.log(`${category}: ${stats.success}/${stats.total} (${rate}%)`);
    });
    
    console.log('');
    
    // Failed tests details
    const failedTests = results.filter(r => r.status === 'failed' || r.status === 'exception');
    if (failedTests.length > 0) {
      console.log('🔍 FAILED TESTS ANALYSIS:');
      failedTests.forEach(test => {
        console.log(`- ${test.command}`);
        console.log(`  Error: ${test.error}`);
        console.log('');
      });
    }
    
  } catch (error) {
    console.error('❌ Overall test error:', error);
  }
}

testAIFunctionality();
