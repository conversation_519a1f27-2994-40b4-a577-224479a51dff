<!DOCTYPE html>
<html>
<head>
    <title>Test Purchase Order Command</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; background: #f5f5f5; border-radius: 5px; }
        .command { background: #e3f2fd; padding: 10px; border-radius: 3px; font-family: monospace; }
        .result { background: #e8f5e8; padding: 10px; border-radius: 3px; margin-top: 10px; }
        .error { background: #ffebee; padding: 10px; border-radius: 3px; margin-top: 10px; }
        button { padding: 10px 20px; background: #2196f3; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #1976d2; }
    </style>
</head>
<body>
    <h1>🧪 Purchase Order Command Test</h1>
    
    <div class="test-section">
        <h3>Command Being Tested:</h3>
        <div class="command">create a purchase order for all low stock items</div>
    </div>

    <div class="test-section">
        <h3>Expected AI Processing:</h3>
        <ul>
            <li>✅ Command should reach Gemini AI (no local blocking)</li>
            <li>✅ AI should parse as: <code>action: "createPurchaseOrder", data: { allLowStock: true }</code></li>
            <li>✅ Should call <code>handleCreatePurchaseOrder()</code> function</li>
            <li>✅ Should use RPC function <code>get_low_stock_items</code></li>
            <li>✅ Should create purchase orders grouped by property</li>
        </ul>
    </div>

    <div class="test-section">
        <h3>Local AI Assistant Test:</h3>
        <button onclick="testLocalAI()">Test Local AI Processing</button>
        <div id="localResult"></div>
    </div>

    <div class="test-section">
        <h3>AI Processor Simulation:</h3>
        <button onclick="simulateAIProcessing()">Simulate AI Processing</button>
        <div id="aiResult"></div>
    </div>

    <script>
        function testLocalAI() {
            const command = "create a purchase order for all low stock items";
            console.log('Testing local AI with command:', command);
            
            // Simulate what would happen in aiLocalAssistant.ts
            const result = {
                shouldProceed: true,
                response: {
                    success: true,
                    message: "Processing your request..."
                }
            };
            
            const resultDiv = document.getElementById('localResult');
            if (result.shouldProceed) {
                resultDiv.innerHTML = `
                    <div class="result">
                        ✅ Local AI Check: PASSED<br>
                        shouldProceed: ${result.shouldProceed}<br>
                        message: "${result.response.message}"<br>
                        <br>
                        <strong>Status:</strong> Command will be sent to Gemini AI for processing
                    </div>
                `;
            } else {
                resultDiv.innerHTML = `
                    <div class="error">
                        ❌ Local AI Check: BLOCKED<br>
                        This would have been the old bug!
                    </div>
                `;
            }
        }

        function simulateAIProcessing() {
            const command = "create a purchase order for all low stock items";
            console.log('Simulating AI processing for command:', command);
            
            // Simulate what Gemini AI should parse this as
            const expectedAIParsing = {
                action: "createPurchaseOrder",
                data: {
                    allLowStock: true
                },
                message: "I'll create a purchase order for all low stock items."
            };
            
            const resultDiv = document.getElementById('aiResult');
            resultDiv.innerHTML = `
                <div class="result">
                    ✅ AI Processing Simulation<br><br>
                    <strong>Parsed Action:</strong> ${expectedAIParsing.action}<br>
                    <strong>Data:</strong> ${JSON.stringify(expectedAIParsing.data, null, 2)}<br>
                    <strong>Message:</strong> "${expectedAIParsing.message}"<br><br>
                    
                    <strong>Next Steps:</strong><br>
                    1. Call handleCreatePurchaseOrder(supabase, userId, data)<br>
                    2. Execute: supabase.rpc('get_low_stock_items', { user_id_param: userId })<br>
                    3. Group items by property<br>
                    4. Create purchase order records<br>
                    5. Return success with order details
                </div>
            `;
        }

        // Auto-run tests when page loads
        window.onload = function() {
            console.log('Running automatic tests...');
            testLocalAI();
            simulateAIProcessing();
        };
    </script>
</body>
</html>