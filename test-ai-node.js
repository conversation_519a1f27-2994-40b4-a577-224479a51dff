/**
 * AI Improvements Node.js Test Suite
 * 
 * Tests AI parsing logic directly without browser dependencies
 */

const { default: fetch } = require('node-fetch');

const AI_NODE_TEST = {
  config: {
    supabaseUrl: 'https://pwaeknalhosfwuxkpaet.supabase.co',
    testUserId: '4f8e65b6-a501-474c-9ecf-8dc38e9dc5a4', // Test user ID
    inventoryTests: [
      {
        command: "Add 10 bath towels to Thames",
        expected: { action: 'addInventoryItem', name: 'bath towels', quantity: 10, property: 'Thames' }
      },
      {
        command: "Update towel quantity to 15 at Thames", 
        expected: { action: 'updateInventoryItem', name: 'towel', quantity: 15, property: 'Thames' }
      },
      {
        command: "Add 5 cleaning supplies to Thames",
        expected: { action: 'addInventoryItem', name: 'cleaning supplies', quantity: 5, property: 'Thames' }
      }
    ],
    purchaseOrderTests: [
      {
        command: "Create a purchase order for all low stock items",
        expected: { action: 'createPurchaseOrder', category: 'general' }
      },
      {
        command: "Order cleaning supplies for Thames",
        expected: { action: 'createPurchaseOrder', category: 'cleaning supplies', property: 'Thames' }
      },
      {
        command: "Order bathroom supplies for Thames",
        expected: { action: 'createPurchaseOrder', category: 'bathroom supplies', property: 'Thames' }
      }
    ]
  },

  async testAICommand(command, testData = {}) {
    console.log(`\n🧪 Testing: "${command}"`);
    
    try {
      const response = await fetch(`${this.config.supabaseUrl}/functions/v1/ai-command-processor`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer test-token`, // Mock token for testing
        },
        body: JSON.stringify({
          command: command,
          userId: this.config.testUserId,
          originalCommand: command
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      
      console.log(`Result: ${result.success ? '✅' : '❌'} ${result.message}`);
      
      if (result.success && result.data) {
        console.log(`   Action: ${result.data.action}`);
        
        // Validate specific fields based on test expectations
        if (testData.expected) {
          const expected = testData.expected;
          
          if (expected.action && result.data.action === expected.action) {
            console.log('   ✅ Action matches expected');
          } else if (expected.action) {
            console.log(`   ❌ Action mismatch: expected ${expected.action}, got ${result.data.action}`);
          }
          
          if (expected.name && result.data.name) {
            if (result.data.name.toLowerCase().includes(expected.name.toLowerCase())) {
              console.log('   ✅ Item name correctly extracted');
            } else {
              console.log(`   ❌ Item name mismatch: expected "${expected.name}", got "${result.data.name}"`);
            }
          }
          
          if (expected.quantity && result.data.quantity === expected.quantity) {
            console.log('   ✅ Quantity correctly parsed');
          } else if (expected.quantity) {
            console.log(`   ❌ Quantity mismatch: expected ${expected.quantity}, got ${result.data.quantity}`);
          }
          
          if (expected.property && result.data.property) {
            if (result.data.property.toLowerCase().includes(expected.property.toLowerCase())) {
              console.log('   ✅ Property correctly identified');
            } else {
              console.log(`   ❌ Property mismatch: expected "${expected.property}", got "${result.data.property}"`);
            }
          }
          
          if (expected.category && result.data.category) {
            if (result.data.category.toLowerCase().includes(expected.category.toLowerCase())) {
              console.log('   ✅ Category correctly assigned');
            } else {
              console.log(`   ❌ Category mismatch: expected "${expected.category}", got "${result.data.category}"`);
            }
          }
        }
        
        // Display parsed data
        console.log(`   Raw Data: ${JSON.stringify(result.data, null, 2).substring(0, 150)}...`);
      }
      
      return result;
      
    } catch (error) {
      console.log(`   ❌ Error: ${error.message}`);
      return { success: false, error: error.message };
    }
  },

  async runInventoryTests() {
    console.log('\n📦 TESTING INVENTORY PARSING IMPROVEMENTS');
    console.log('='.repeat(45));
    
    let passed = 0;
    let failed = 0;
    
    for (const test of this.config.inventoryTests) {
      const result = await this.testAICommand(test.command, test);
      
      if (result.success) {
        passed++;
      } else {
        failed++;
      }
      
      await this.delay(500);
    }
    
    console.log(`\n📊 Inventory Tests: ${passed} passed, ${failed} failed`);
    return { passed, failed };
  },

  async runPurchaseOrderTests() {
    console.log('\n🛒 TESTING PURCHASE ORDER PARSING IMPROVEMENTS');
    console.log('='.repeat(50));
    
    let passed = 0;
    let failed = 0;
    
    for (const test of this.config.purchaseOrderTests) {
      const result = await this.testAICommand(test.command, test);
      
      if (result.success) {
        passed++;
      } else {
        failed++;
      }
      
      await this.delay(500);
    }
    
    console.log(`\n📊 Purchase Order Tests: ${passed} passed, ${failed} failed`);
    return { passed, failed };
  },

  async runAllTests() {
    console.log('🚀 AI IMPROVEMENTS NODE.JS TEST SUITE');
    console.log('Testing Enhanced Parsing Logic');
    console.log('='.repeat(50));
    
    const inventoryResults = await this.runInventoryTests();
    const purchaseOrderResults = await this.runPurchaseOrderTests();
    
    const totalPassed = inventoryResults.passed + purchaseOrderResults.passed;
    const totalFailed = inventoryResults.failed + purchaseOrderResults.failed;
    const totalTests = totalPassed + totalFailed;
    
    console.log('\n🏆 FINAL RESULTS');
    console.log('='.repeat(20));
    console.log(`Total Tests Run: ${totalTests}`);
    console.log(`Passed: ${totalPassed} (${Math.round(totalPassed/totalTests*100)}%)`);
    console.log(`Failed: ${totalFailed} (${Math.round(totalFailed/totalTests*100)}%)`);
    
    if (totalPassed === totalTests) {
      console.log('\n✨ All tests passed! AI improvements are working correctly.');
    } else {
      console.log('\n⚠️ Some tests failed. Check the output above for details.');
    }
  },

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
};

// Check if we're running directly
if (require.main === module) {
  console.log('Starting AI improvements test suite...\n');
  AI_NODE_TEST.runAllTests().catch(console.error);
}

module.exports = AI_NODE_TEST;
