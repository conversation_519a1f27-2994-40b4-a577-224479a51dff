// Quick test to verify Supabase cloud connection
import { supabase } from './src/integrations/supabase/client.js';

async function testSupabaseConnection() {
  console.log('🧪 Testing Supabase connection...');
  
  try {
    // Test basic connection
    const { data, error } = await supabase.from('profiles').select('count').limit(1);
    
    if (error) {
      console.error('❌ Connection test failed:', error);
      return false;
    }
    
    console.log('✅ Supabase connection successful!');
    console.log('Response:', data);
    return true;
  } catch (err) {
    console.error('❌ Connection test error:', err);
    return false;
  }
}

testSupabaseConnection();
