<!DOCTYPE html>
<html>
<head>
    <title>Debug Browser Authentication</title>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; background: #f5f5f5; border-radius: 5px; }
        .result { background: #e8f5e8; padding: 10px; border-radius: 3px; margin: 10px 0; }
        .error { background: #ffebee; padding: 10px; border-radius: 3px; margin: 10px 0; }
        button { padding: 10px 20px; background: #2196f3; color: white; border: none; border-radius: 3px; cursor: pointer; margin: 5px; }
        button:hover { background: #1976d2; }
        pre { background: #f0f0f0; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🔍 Browser Authentication Debug</h1>
    
    <div class="section">
        <h3>Step 1: Initialize Supabase Client</h3>
        <button onclick="initSupabase()">Initialize Supabase</button>
        <div id="initResult"></div>
    </div>

    <div class="section">
        <h3>Step 2: Login</h3>
        <button onclick="login()">Login as Test User</button>
        <div id="loginResult"></div>
    </div>

    <div class="section">
        <h3>Step 3: Test AI Command</h3>
        <button onclick="testAICommand()">Test Purchase Order Command</button>
        <div id="aiResult"></div>
    </div>

    <div class="section">
        <h3>Debug Logs</h3>
        <div id="debugLogs"></div>
    </div>

    <script>
        let supabase;
        let debugLogs = [];

        function log(message) {
            console.log(message);
            debugLogs.push(`${new Date().toISOString()}: ${message}`);
            document.getElementById('debugLogs').innerHTML = 
                '<pre>' + debugLogs.join('\n') + '</pre>';
        }

        function showResult(elementId, success, message, data = null) {
            const element = document.getElementById(elementId);
            element.innerHTML = `
                <div class="${success ? 'result' : 'error'}">
                    ${message}
                    ${data ? '<pre>' + JSON.stringify(data, null, 2) + '</pre>' : ''}
                </div>
            `;
        }

        function initSupabase() {
            try {
                log('Initializing Supabase client...');
                
                const SUPABASE_URL = 'https://pwaeknalhosfwuxkpaet.supabase.co';
                const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB3YWVrbmFsaG9zZnd1eGtwYWV0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDEwMjk1MzMsImV4cCI6MjA1NjYwNTUzM30.buZMBA9gPIkvp3rIGJl-QeYIO2IXUC8ea_N-AvLJID4';
                
                supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {
                    auth: {
                        autoRefreshToken: true,
                        persistSession: true,
                        detectSessionInUrl: true,
                        flowType: 'pkce'
                    }
                });
                
                log('Supabase client initialized successfully');
                showResult('initResult', true, 'Supabase client initialized successfully');
                
            } catch (error) {
                log('Error initializing Supabase: ' + error.message);
                showResult('initResult', false, 'Error initializing Supabase: ' + error.message);
            }
        }

        async function login() {
            try {
                log('Attempting login...');
                
                const { data, error } = await supabase.auth.signInWithPassword({
                    email: '<EMAIL>',
                    password: 'Newsig1!!!'
                });
                
                if (error) {
                    log('Login error: ' + error.message);
                    showResult('loginResult', false, 'Login failed: ' + error.message);
                    return;
                }
                
                log('Login successful, user ID: ' + data.user.id);
                showResult('loginResult', true, 'Login successful!', {
                    userId: data.user.id,
                    email: data.user.email
                });
                
                // Also test session immediately
                const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
                if (sessionError) {
                    log('Session check failed: ' + sessionError.message);
                } else {
                    log('Session verified: ' + sessionData.session.user.id);
                }
                
            } catch (error) {
                log('Login exception: ' + error.message);
                showResult('loginResult', false, 'Login exception: ' + error.message);
            }
        }

        async function testAICommand() {
            try {
                log('Testing AI command...');
                
                // First check session
                const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
                if (sessionError || !sessionData.session) {
                    log('Session check failed: ' + (sessionError?.message || 'No session'));
                    showResult('aiResult', false, 'Session expired. Please login first.');
                    return;
                }
                
                log('Session valid for user: ' + sessionData.session.user.id);
                
                // Call AI command processor
                const { data, error } = await supabase.functions.invoke('ai-command-processor', {
                    body: {
                        command: 'create a purchase order for all low stock items',
                        originalCommand: 'create a purchase order for all low stock items',
                        hasContext: false
                    }
                });
                
                if (error) {
                    log('AI command error: ' + error.message);
                    showResult('aiResult', false, 'AI command failed: ' + error.message, error);
                    return;
                }
                
                log('AI command successful!');
                showResult('aiResult', true, 'AI command successful!', data);
                
            } catch (error) {
                log('AI command exception: ' + error.message);
                showResult('aiResult', false, 'AI command exception: ' + error.message);
            }
        }

        // Auto-initialize when page loads
        window.onload = function() {
            initSupabase();
        };
    </script>
</body>
</html>
