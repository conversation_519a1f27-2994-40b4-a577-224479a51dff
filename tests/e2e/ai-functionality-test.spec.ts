import { test, expect } from '@playwright/test';

const TEST_EMAIL = '<EMAIL>';
const TEST_PASSWORD = 'Newsig1!!!';

test.describe('AI Functionality Testing', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to login page
    await page.goto('/');
    
    // Wait for page to load and check if already logged in
    await page.waitForLoadState('networkidle');
    
    // Check if we're already on dashboard (logged in)
    const currentUrl = page.url();
    if (currentUrl.includes('/dashboard')) {
      console.log('Already logged in, continuing with tests...');
      return;
    }
    
    // If not logged in, perform login
    console.log('Logging in with test credentials...');
    
    // Look for login form or redirect to login
    const emailInput = page.locator('input[type="email"], input[name="email"], input[placeholder*="email" i]').first();
    const passwordInput = page.locator('input[type="password"], input[name="password"], input[placeholder*="password" i]').first();
    
    await emailInput.fill(TEST_EMAIL);
    await passwordInput.fill(TEST_PASSWORD);
    
    // Find and click login button
    const loginButton = page.locator('button:has-text("Sign In"), button:has-text("Login"), button:has-text("Log In"), button[type="submit"]').first();
    await loginButton.click();
    
    // Wait for login to complete and redirect to dashboard
    await page.waitForURL('**/dashboard**', { timeout: 10000 });
    await page.waitForLoadState('networkidle');
    
    console.log('Login successful, now on dashboard');
  });

  test('Test AI Command Center - Add Inventory Item', async ({ page }) => {
    console.log('Testing AI inventory addition...');
    
    // Navigate to dashboard if not already there
    await page.goto('/dashboard');
    await page.waitForLoadState('networkidle');
    
    // Look for AI Command Center
    const aiCommandCenter = page.locator('[data-testid="ai-command-center"], .ai-command-center, *:has-text("AI Command Center"), *:has-text("AI Assistant")').first();
    
    if (await aiCommandCenter.count() > 0) {
      console.log('Found AI Command Center, testing inventory addition...');
      
      // Find the AI input/textarea
      const aiInput = page.locator('input[placeholder*="AI" i], textarea[placeholder*="AI" i], input[placeholder*="command" i], textarea[placeholder*="command" i]').first();
      
      if (await aiInput.count() > 0) {
        // Test adding inventory item
        await aiInput.fill('Add 5 towels to Beach House');
        
        // Look for submit/send button
        const submitButton = page.locator('button:has-text("Send"), button:has-text("Submit"), button[type="submit"]').first();
        await submitButton.click();
        
        // Wait for response
        await page.waitForTimeout(3000);
        
        // Check for success message or response
        const responseArea = page.locator('.response, .ai-response, .message, .result').first();
        if (await responseArea.count() > 0) {
          const responseText = await responseArea.textContent();
          console.log('AI Response:', responseText);
          expect(responseText).toContain('towel');
        }
      } else {
        console.log('AI input not found, checking alternative selectors...');
        // Try alternative ways to find AI input
        await page.screenshot({ path: 'ai-command-center-debug.png' });
      }
    } else {
      console.log('AI Command Center not found, navigating to inventory page instead...');
      
      // Navigate to inventory page as fallback
      await page.goto('/inventory');
      await page.waitForLoadState('networkidle');
      
      // Look for add item functionality
      const addButton = page.locator('button:has-text("Add"), button:has-text("New"), button:has-text("Create")').first();
      if (await addButton.count() > 0) {
        await addButton.click();
        await page.waitForTimeout(1000);
        
        // Fill in item details
        const nameInput = page.locator('input[name="name"], input[placeholder*="name" i]').first();
        if (await nameInput.count() > 0) {
          await nameInput.fill('Test Towel Set');
          
          const quantityInput = page.locator('input[name="quantity"], input[placeholder*="quantity" i], input[type="number"]').first();
          if (await quantityInput.count() > 0) {
            await quantityInput.fill('5');
          }
          
          // Submit the form
          const saveButton = page.locator('button:has-text("Save"), button:has-text("Add"), button:has-text("Create"), button[type="submit"]').first();
          await saveButton.click();
          
          await page.waitForTimeout(2000);
          console.log('Manual inventory item added successfully');
        }
      }
    }
  });

  test('Test AI Command Center - Create Maintenance Task', async ({ page }) => {
    console.log('Testing AI maintenance task creation...');
    
    await page.goto('/dashboard');
    await page.waitForLoadState('networkidle');
    
    // Look for AI Command Center
    const aiCommandCenter = page.locator('[data-testid="ai-command-center"], .ai-command-center, *:has-text("AI Command Center"), *:has-text("AI Assistant")').first();
    
    if (await aiCommandCenter.count() > 0) {
      const aiInput = page.locator('input[placeholder*="AI" i], textarea[placeholder*="AI" i], input[placeholder*="command" i], textarea[placeholder*="command" i]').first();
      
      if (await aiInput.count() > 0) {
        // Test creating maintenance task
        await aiInput.fill('Fix broken window at Beach House, assign to John Smith');
        
        const submitButton = page.locator('button:has-text("Send"), button:has-text("Submit"), button[type="submit"]').first();
        await submitButton.click();
        
        await page.waitForTimeout(3000);
        
        const responseArea = page.locator('.response, .ai-response, .message, .result').first();
        if (await responseArea.count() > 0) {
          const responseText = await responseArea.textContent();
          console.log('AI Maintenance Response:', responseText);
          expect(responseText).toContain('maintenance');
        }
      }
    } else {
      console.log('AI Command Center not found, testing maintenance page directly...');
      
      // Navigate to maintenance page as fallback
      await page.goto('/maintenance');
      await page.waitForLoadState('networkidle');
      
      const addTaskButton = page.locator('button:has-text("Add"), button:has-text("New"), button:has-text("Create")').first();
      if (await addTaskButton.count() > 0) {
        await addTaskButton.click();
        await page.waitForTimeout(1000);
        
        // Fill in task details
        const titleInput = page.locator('input[name="title"], input[placeholder*="title" i]').first();
        if (await titleInput.count() > 0) {
          await titleInput.fill('Test Window Repair');
          
          const descriptionInput = page.locator('textarea[name="description"], textarea[placeholder*="description" i]').first();
          if (await descriptionInput.count() > 0) {
            await descriptionInput.fill('Fix broken window in living room');
          }
          
          const saveButton = page.locator('button:has-text("Save"), button:has-text("Add"), button:has-text("Create"), button[type="submit"]').first();
          await saveButton.click();
          
          await page.waitForTimeout(2000);
          console.log('Manual maintenance task created successfully');
        }
      }
    }
  });

  test('Test Property Management and Navigation', async ({ page }) => {
    console.log('Testing property management...');
    
    // Navigate to properties page
    await page.goto('/properties');
    await page.waitForLoadState('networkidle');
    
    // Check if properties are loaded
    const propertiesContainer = page.locator('.property, .property-card, [data-testid="property"]').first();
    if (await propertiesContainer.count() > 0) {
      console.log('Properties found on page');
      
      // Take a screenshot for verification
      await page.screenshot({ path: 'properties-page.png' });
      
      // Test property details view
      await propertiesContainer.click();
      await page.waitForTimeout(2000);
      
      console.log('Property details opened successfully');
    } else {
      console.log('No properties found, testing add property functionality...');
      
      const addPropertyButton = page.locator('button:has-text("Add"), button:has-text("New"), button:has-text("Create")').first();
      if (await addPropertyButton.count() > 0) {
        await addPropertyButton.click();
        await page.waitForTimeout(1000);
        
        // Fill in property details
        const nameInput = page.locator('input[name="name"], input[placeholder*="name" i]').first();
        if (await nameInput.count() > 0) {
          await nameInput.fill('Test Beach House');
          
          const addressInput = page.locator('input[name="address"], input[placeholder*="address" i]').first();
          if (await addressInput.count() > 0) {
            await addressInput.fill('123 Ocean Drive');
          }
          
          const saveButton = page.locator('button:has-text("Save"), button:has-text("Add"), button:has-text("Create"), button[type="submit"]').first();
          await saveButton.click();
          
          await page.waitForTimeout(2000);
          console.log('Test property created successfully');
        }
      }
    }
  });

  test('Test Inventory Page Functionality', async ({ page }) => {
    console.log('Testing inventory page functionality...');
    
    await page.goto('/inventory');
    await page.waitForLoadState('networkidle');
    
    // Take screenshot of inventory page
    await page.screenshot({ path: 'inventory-page.png' });
    
    // Check for inventory items
    const inventoryItems = page.locator('.inventory-item, .item, [data-testid="inventory-item"]');
    const itemCount = await inventoryItems.count();
    console.log(`Found ${itemCount} inventory items`);
    
    if (itemCount > 0) {
      // Test editing an existing item
      const firstItem = inventoryItems.first();
      const editButton = firstItem.locator('button:has-text("Edit"), button:has-text("Update"), .edit-btn').first();
      
      if (await editButton.count() > 0) {
        await editButton.click();
        await page.waitForTimeout(1000);
        
        // Update quantity
        const quantityInput = page.locator('input[name="quantity"], input[placeholder*="quantity" i], input[type="number"]').first();
        if (await quantityInput.count() > 0) {
          await quantityInput.clear();
          await quantityInput.fill('10');
          
          const saveButton = page.locator('button:has-text("Save"), button:has-text("Update"), button[type="submit"]').first();
          await saveButton.click();
          
          await page.waitForTimeout(2000);
          console.log('Inventory item updated successfully');
        }
      }
    }
    
    // Test filtering functionality
    const filterInput = page.locator('input[placeholder*="filter" i], input[placeholder*="search" i]').first();
    if (await filterInput.count() > 0) {
      await filterInput.fill('towel');
      await page.waitForTimeout(1000);
      console.log('Filter applied successfully');
    }
  });

  test('Test Dashboard Overview', async ({ page }) => {
    console.log('Testing dashboard overview...');
    
    await page.goto('/dashboard');
    await page.waitForLoadState('networkidle');
    
    // Take screenshot of dashboard
    await page.screenshot({ path: 'dashboard-overview.png' });
    
    // Check for key dashboard elements
    const dashboardCards = page.locator('.card, .dashboard-card, [data-testid="dashboard-card"]');
    const cardCount = await dashboardCards.count();
    console.log(`Found ${cardCount} dashboard cards`);
    
    // Check for stats/metrics
    const statsElements = page.locator('.stat, .metric, .count, .number');
    const statsCount = await statsElements.count();
    console.log(`Found ${statsCount} statistics elements`);
    
    // Check for recent activity or notifications
    const activityElements = page.locator('.activity, .notification, .recent, .alert');
    const activityCount = await activityElements.count();
    console.log(`Found ${activityCount} activity elements`);
    
    // Verify navigation menu is present
    const navMenu = page.locator('nav, .navigation, .sidebar, .menu').first();
    expect(await navMenu.count()).toBeGreaterThan(0);
    console.log('Navigation menu found');
  });

  test('Test AI Command Variations', async ({ page }) => {
    console.log('Testing various AI command variations...');
    
    await page.goto('/dashboard');
    await page.waitForLoadState('networkidle');
    
    const aiInput = page.locator('input[placeholder*="AI" i], textarea[placeholder*="AI" i], input[placeholder*="command" i], textarea[placeholder*="command" i]').first();
    const submitButton = page.locator('button:has-text("Send"), button:has-text("Submit"), button[type="submit"]').first();
    
    if (await aiInput.count() > 0 && await submitButton.count() > 0) {
      // Test different AI commands
      const commands = [
        'Add 3 towels to Beach House',
        'Create maintenance task for fixing broken sink at Ocean View',
        'Add property named Test Villa at 456 Beach Road',
        'Update inventory for cleaning supplies',
        'Create purchase order for low stock items'
      ];
      
      for (const command of commands) {
        console.log(`Testing command: ${command}`);
        
        await aiInput.clear();
        await aiInput.fill(command);
        await submitButton.click();
        
        // Wait for response
        await page.waitForTimeout(3000);
        
        // Check for response
        const responseArea = page.locator('.response, .ai-response, .message, .result').first();
        if (await responseArea.count() > 0) {
          const responseText = await responseArea.textContent();
          console.log(`Response for "${command}":`, responseText?.substring(0, 100) + '...');
        }
        
        // Wait between commands
        await page.waitForTimeout(1000);
      }
    } else {
      console.log('AI input or submit button not found');
      await page.screenshot({ path: 'ai-input-debug.png' });
    }
  });
});