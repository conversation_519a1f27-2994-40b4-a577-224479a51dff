import React from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

interface ConfirmDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title?: string;
  description?: string;
  confirmText?: string;
  cancelText?: string;
  isLoading?: boolean;
  variant?: 'default' | 'destructive';
}

export const ConfirmDialog: React.FC<ConfirmDialogProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title = "Are you sure?",
  description = "This action cannot be undone.",
  confirmText = "Confirm",
  cancelText = "Cancel",
  isLoading = false,
  variant = 'default'
}) => {
  return (
    <AlertDialog open={isOpen} onOpenChange={onClose}>
      <AlertDialogContent className="glass-modal">
        <AlertDialogHeader>
          <AlertDialogTitle className="text-glass">{title}</AlertDialogTitle>
          <AlertDialogDescription className="text-glass-muted">
            {description}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isLoading} className="glass-interactive">
            {cancelText}
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={onConfirm}
            disabled={isLoading}
            className={`glass-interactive ${
              variant === 'destructive' 
                ? 'bg-red-500/80 text-white hover:bg-red-600/80 border-red-500/20' 
                : 'bg-primary/80 text-white hover:bg-primary/90'
            }`}
          >
            {isLoading ? 'Processing...' : confirmText}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

// Hook for easy usage
export const useConfirmDialog = () => {
  const [isOpen, setIsOpen] = React.useState(false);
  const [dialogProps, setDialogProps] = React.useState<Partial<ConfirmDialogProps>>({});
  const confirmResolve = React.useRef<((value: boolean) => void) | null>(null);

  const confirm = React.useCallback((props: Partial<ConfirmDialogProps> = {}): Promise<boolean> => {
    return new Promise((resolve) => {
      setDialogProps(props);
      setIsOpen(true);
      confirmResolve.current = resolve;
    });
  }, []);

  const handleConfirm = React.useCallback(() => {
    setIsOpen(false);
    confirmResolve.current?.(true);
    confirmResolve.current = null;
  }, []);

  const handleCancel = React.useCallback(() => {
    setIsOpen(false);
    confirmResolve.current?.(false);
    confirmResolve.current = null;
  }, []);

  const DialogComponent = React.useCallback(() => (
    <ConfirmDialog
      isOpen={isOpen}
      onClose={handleCancel}
      onConfirm={handleConfirm}
      {...dialogProps}
    />
  ), [isOpen, handleCancel, handleConfirm, dialogProps]);

  return { confirm, DialogComponent };
};
