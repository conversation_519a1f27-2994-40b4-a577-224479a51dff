import React from 'react';
import { Building2, Wrench, Package, AlertCircle, ShoppingCart, BarChart3 } from 'lucide-react';
import StatCard from '@/components/ui/StatCard';
import GlassCard from '@/components/ui/GlassCard';

const GlassmorphicDemo = () => {
  const statCards = [
    {
      title: 'Properties',
      value: 12,
      icon: Building2,
      colorScheme: 'blue' as const,
      subtitle: '8 occupied, 4 vacant'
    },
    {
      title: 'Critical Tasks',
      value: 3,
      icon: Wrench,
      colorScheme: 'amber' as const,
      alert: true,
      subtitle: 'Requires immediate attention'
    },
    {
      title: 'Low Stock Items',
      value: 7,
      icon: Package,
      colorScheme: 'purple' as const,
      alert: true,
      subtitle: 'Need restocking'
    },
    {
      title: 'Pending Orders',
      value: 5,
      icon: ShoppingCart,
      colorScheme: 'green' as const,
      subtitle: 'Awaiting processing'
    },
  ];

  return (
    <div className="dashboard-background min-h-screen p-6">
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Adobe-Style Header Banner with Heavy Blur */}
        <GlassCard className="p-8 mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-6">
              <div className="p-4 rounded-3xl glass-interactive">
                <BarChart3 className="h-10 w-10 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <h1 className="text-5xl font-bold text-glass leading-tight">
                  Professional Glassmorphic Dashboard
                </h1>
                <p className="text-glass-muted text-xl font-medium mt-2">
                  Adobe Creative Cloud inspired interface with premium glass effects
                </p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-4 h-4 bg-green-500 rounded-full animate-pulse shadow-lg shadow-green-500/50"></div>
              <span className="text-glass-muted text-lg font-semibold">Live</span>
            </div>
          </div>
        </GlassCard>

        {/* Enhanced Overview Section with True Frosted Glass */}
        <GlassCard className="p-8">
          <div className="flex items-center justify-between mb-8">
            <h2 className="text-3xl font-bold text-glass flex items-center gap-4">
              <div className="w-2 h-10 bg-gradient-to-b from-blue-500 to-purple-600 rounded-full"></div>
              Overview
            </h2>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
            {statCards.map((stat, index) => (
              <div 
                key={index}
                style={{ 
                  animationDelay: `${index * 200}ms`,
                  animationFillMode: 'both'
                }}
                className="animate-fadeInUp"
              >
                <StatCard
                  title={stat.title}
                  value={stat.value}
                  icon={stat.icon}
                  colorScheme={stat.colorScheme}
                  alert={stat.alert}
                  subtitle={stat.subtitle}
                  onClick={() => console.log(`Clicked ${stat.title}`)}
                  className="transition-all duration-300 hover:scale-105 cursor-pointer"
                />
              </div>
            ))}
          </div>
        </GlassCard>

        {/* Professional Content Grid with Heavy Glass Blur */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          
          {/* Properties Portfolio Section */}
          <div className="lg:col-span-2 space-y-8">
            
            <GlassCard className="p-8">
              <div className="flex items-center justify-between mb-8">
                <h3 className="text-2xl font-bold text-glass flex items-center gap-4">
                  <Building2 className="h-7 w-7 text-blue-500" />
                  Properties Overview
                </h3>
                <button className="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 font-semibold transition-colors text-lg">
                  View All →
                </button>
              </div>
              
              <div className="space-y-8">
                <div className="grid grid-cols-2 gap-6">
                  <div className="glass-interactive p-8 text-center space-y-4">
                    <div className="w-6 h-6 bg-green-500 rounded-full mx-auto shadow-lg shadow-green-500/50"></div>
                    <div className="text-3xl font-bold text-glass">8</div>
                    <span className="text-base font-semibold text-glass-muted">Occupied</span>
                  </div>
                  <div className="glass-interactive p-8 text-center space-y-4">
                    <div className="w-6 h-6 bg-red-500 rounded-full mx-auto shadow-lg shadow-red-500/50"></div>
                    <div className="text-3xl font-bold text-glass">4</div>
                    <span className="text-base font-semibold text-glass-muted">Vacant</span>
                  </div>
                </div>

                <div className="space-y-4">
                  {[
                    { name: 'Sunset Villa', status: 'occupied', color: 'green' },
                    { name: 'Ocean View Apartment', status: 'vacant', color: 'red' },
                    { name: 'Mountain Lodge', status: 'occupied', color: 'green' },
                    { name: 'City Center Loft', status: 'vacant', color: 'red' },
                  ].map((property, index) => (
                    <div
                      key={index}
                      className="glass-interactive p-6 flex justify-between items-center cursor-pointer transition-all duration-300 hover:scale-[1.02]"
                    >
                      <span className="font-semibold text-glass text-lg">{property.name}</span>
                      <div className="flex items-center gap-4">
                        <div className={`w-4 h-4 rounded-full shadow-lg ${property.color === 'green' ? 'bg-green-500 shadow-green-500/50' : 'bg-red-500 shadow-red-500/50'}`}></div>
                        <span className={`text-sm font-bold px-4 py-2 rounded-full ${property.color === 'green' ? 'bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300' : 'bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-300'}`}>
                          {property.status}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </GlassCard>

            {/* Maintenance Tasks Section */}
            <GlassCard className="p-8">
              <div className="flex items-center justify-between mb-8">
                <h3 className="text-2xl font-bold text-glass flex items-center gap-4">
                  <Wrench className="h-7 w-7 text-amber-500" />
                  Maintenance Tasks
                </h3>
                <button className="text-amber-600 hover:text-amber-700 dark:text-amber-400 dark:hover:text-amber-300 font-semibold transition-colors text-lg">
                  Manage All →
                </button>
              </div>
              
              <div className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div className="glass-interactive p-6 text-center">
                    <div className="text-2xl font-bold text-red-500">3</div>
                    <span className="text-sm text-glass-muted">Critical</span>
                  </div>
                  <div className="glass-interactive p-6 text-center">
                    <div className="text-2xl font-bold text-amber-500">5</div>
                    <span className="text-sm text-glass-muted">High</span>
                  </div>
                </div>

                <div className="space-y-4">
                  {[
                    { task: 'HVAC System Repair', priority: 'Critical Priority', status: 'new' },
                    { task: 'Kitchen Sink Leak', priority: 'High Priority', status: 'in_progress' },
                    { task: 'Door Lock Replacement', priority: 'Medium Priority', status: 'assigned' },
                    { task: 'Paint Touch-up', priority: 'Low Priority', status: 'new' },
                  ].map((item, index) => (
                    <div key={index} className="glass-interactive p-6 flex justify-between items-center cursor-pointer transition-all duration-300 hover:scale-[1.02]">
                      <div>
                        <span className="font-semibold text-glass block text-lg">{item.task}</span>
                        <div className="text-base text-glass-muted mt-1">{item.priority}</div>
                      </div>
                      <span className={`text-sm px-4 py-2 rounded-full font-bold shadow-lg ${
                        item.status === 'new' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-200' :
                        item.status === 'in_progress' ? 'bg-amber-100 text-amber-800 dark:bg-amber-900/50 dark:text-amber-200' :
                        'bg-purple-100 text-purple-800 dark:bg-purple-900/50 dark:text-purple-200'
                      }`}>
                        {item.status.replace('_', ' ')}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </GlassCard>
          </div>

          {/* Action Items Panel */}
          <div className="space-y-8">
            <GlassCard className="p-8">
              <div className="flex items-center justify-between mb-8">
                <h3 className="text-2xl font-bold text-glass flex items-center gap-4">
                  <AlertCircle className="h-7 w-7 text-purple-500" />
                  Action Items
                </h3>
              </div>
              
              <div className="space-y-6">
                <div className="glass-interactive p-8 text-center space-y-4 transition-all duration-300 hover:scale-105">
                  <div className="text-xl">🔥</div>
                  <h4 className="font-bold text-glass text-lg">Critical Tasks</h4>
                  <div className="text-4xl font-bold text-red-500">3</div>
                  <div className="space-y-2">
                    <div className="text-glass-muted">• HVAC System Repair</div>
                    <div className="text-glass-muted">• Emergency Plumbing</div>
                  </div>
                </div>
                
                <div className="glass-interactive p-8 text-center space-y-4 transition-all duration-300 hover:scale-105">
                  <div className="text-xl">📦</div>
                  <h4 className="font-bold text-glass text-lg">Low Stock</h4>
                  <div className="text-4xl font-bold text-purple-500">7</div>
                  <div className="space-y-2">
                    <div className="text-glass-muted">• Cleaning Supplies</div>
                    <div className="text-glass-muted">• Toilet Paper</div>
                  </div>
                </div>
                
                <div className="glass-interactive p-8 text-center space-y-4 transition-all duration-300 hover:scale-105">
                  <div className="text-xl">🛒</div>
                  <h4 className="font-bold text-glass text-lg">Pending Orders</h4>
                  <div className="text-4xl font-bold text-green-500">5</div>
                  <div className="space-y-2">
                    <div className="text-glass-muted">• Order #1234</div>
                    <div className="text-glass-muted">• Order #1235</div>
                  </div>
                </div>
              </div>
            </GlassCard>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GlassmorphicDemo;