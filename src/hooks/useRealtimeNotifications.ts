import { useState, useEffect, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';

export interface NotificationData {
  id: string;
  user_id: string;
  team_id: string;
  type: 'maintenance_assigned' | 'damage_assigned' | 'task_due' | 'task_completed' | 'inventory_low' | 'system_notification';
  title: string;
  body: string;
  data: any;
  icon?: string;
  badge?: string;
  action_url?: string;
  priority: 'low' | 'normal' | 'high' | 'urgent';
  is_read: boolean;
  delivery_status: 'pending' | 'sent' | 'failed' | 'delivered';
  sent_at?: string;
  read_at?: string;
  expires_at?: string;
  created_at: string;
}

export interface NotificationPreferences {
  maintenance_assigned: {
    push: boolean;
    email: boolean;
    desktop: boolean;
  };
  damage_assigned: {
    push: boolean;
    email: boolean;
    desktop: boolean;
  };
  task_due: {
    push: boolean;
    email: boolean;
    desktop: boolean;
  };
  task_completed: {
    push: boolean;
    email: boolean;
    desktop: boolean;
  };
  inventory_low: {
    push: boolean;
    email: boolean;
    desktop: boolean;
  };
  quiet_hours: {
    enabled: boolean;
    start: string;
    end: string;
  };
}

export const useRealtimeNotifications = () => {
  const { authState } = useAuth();
  const [notifications, setNotifications] = useState<NotificationData[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [preferences, setPreferences] = useState<NotificationPreferences | null>(null);
  const [loading, setLoading] = useState(true);

  // Load initial notifications and preferences
  useEffect(() => {
    if (!authState.user?.id) {
      setLoading(false);
      return;
    }

    loadInitialData();
  }, [authState.user?.id]);

  // Set up real-time subscriptions
  useEffect(() => {
    if (!authState.user?.id) return;

    const notificationChannel = supabase
      .channel('user_notifications')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'notifications',
          filter: `user_id=eq.${authState.user.id}`
        },
        (payload) => {
          const newNotification = payload.new as NotificationData;
          handleNewNotification(newNotification);
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'notifications',
          filter: `user_id=eq.${authState.user.id}`
        },
        (payload) => {
          const updatedNotification = payload.new as NotificationData;
          handleNotificationUpdate(updatedNotification);
        }
      )
      .subscribe();

    // Subscribe to maintenance task assignments
    const maintenanceChannel = supabase
      .channel('maintenance_assignments')
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'maintenance_tasks',
          filter: `assigned_to=eq.${authState.user.id}`
        },
        (payload) => {
          handleMaintenanceAssignment(payload.new);
        }
      )
      .subscribe();

    // Subscribe to damage report assignments
    const damageChannel = supabase
      .channel('damage_assignments')
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'damage_reports',
          filter: `provider_id=eq.${authState.user.id}`
        },
        (payload) => {
          handleDamageAssignment(payload.new);
        }
      )
      .subscribe();

    return () => {
      notificationChannel.unsubscribe();
      maintenanceChannel.unsubscribe();
      damageChannel.unsubscribe();
    };
  }, [authState.user?.id]);

  const loadInitialData = async () => {
    if (!authState.user?.id) return;

    try {
      // Load notifications
      const { data: notificationsData, error: notificationsError } = await supabase
        .from('notifications')
        .select('*')
        .eq('user_id', authState.user.id)
        .order('created_at', { ascending: false })
        .limit(50);

      if (notificationsError) throw notificationsError;

      setNotifications(notificationsData || []);
      setUnreadCount((notificationsData || []).filter(n => !n.is_read).length);

      // Load preferences
      const { data: settingsData, error: settingsError } = await supabase
        .from('user_settings')
        .select('notification_preferences')
        .eq('user_id', authState.user.id)
        .single();

      if (settingsError && settingsError.code !== 'PGRST116') {
        throw settingsError;
      }

      if (settingsData?.notification_preferences) {
        setPreferences(settingsData.notification_preferences);
      }
    } catch (error) {
      console.error('Failed to load initial notification data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleNewNotification = useCallback((notification: NotificationData) => {
    setNotifications(prev => [notification, ...prev]);
    setUnreadCount(prev => prev + 1);

    // Show desktop notification if enabled and not in quiet hours
    if (shouldShowDesktopNotification(notification)) {
      showDesktopNotification(notification);
    }

    // Trigger push notification
    triggerPushNotification(notification);
  }, [preferences]);

  const handleNotificationUpdate = useCallback((notification: NotificationData) => {
    setNotifications(prev => 
      prev.map(n => n.id === notification.id ? notification : n)
    );

    // Update unread count
    setUnreadCount(prev => {
      const wasRead = prev > 0;
      const newCount = notifications.filter(n => 
        n.id === notification.id ? !notification.is_read : !n.is_read
      ).length;
      return newCount;
    });
  }, [notifications]);

  const shouldShowDesktopNotification = (notification: NotificationData): boolean => {
    if (!preferences) return false;

    const typePrefs = preferences[notification.type as keyof NotificationPreferences];
    if (!typePrefs || typeof typePrefs === 'object' && !('desktop' in typePrefs)) return false;

    const desktopEnabled = (typePrefs as any).desktop;
    if (!desktopEnabled) return false;

    // Check quiet hours
    if (preferences.quiet_hours.enabled) {
      const now = new Date();
      const currentTime = now.getHours() * 100 + now.getMinutes();
      const startTime = parseInt(preferences.quiet_hours.start.replace(':', ''));
      const endTime = parseInt(preferences.quiet_hours.end.replace(':', ''));

      if (startTime > endTime) {
        // Quiet hours span midnight
        if (currentTime >= startTime || currentTime <= endTime) {
          return false;
        }
      } else {
        // Normal quiet hours
        if (currentTime >= startTime && currentTime <= endTime) {
          return false;
        }
      }
    }

    return true;
  };

  const showDesktopNotification = (notification: NotificationData) => {
    if ('Notification' in window && Notification.permission === 'granted') {
      const desktopNotification = new Notification(notification.title, {
        body: notification.body,
        icon: notification.icon || '/icons/icon-192x192.png',
        badge: notification.badge || '/icons/logo.png',
        tag: notification.id,
        data: {
          ...notification.data,
          notification_id: notification.id,
          action_url: notification.action_url
        },
        requireInteraction: notification.priority === 'urgent',
        silent: notification.priority === 'low'
      });

      desktopNotification.onclick = () => {
        window.focus();
        if (notification.action_url) {
          window.location.href = notification.action_url;
        }
        markAsRead(notification.id);
        desktopNotification.close();
      };

      // Auto-close after 5 seconds for non-urgent notifications
      if (notification.priority !== 'urgent') {
        setTimeout(() => {
          desktopNotification.close();
        }, 5000);
      }
    }
  };

  const triggerPushNotification = async (notification: NotificationData) => {
    try {
      await supabase.functions.invoke('send-push-notification', {
        body: {
          user_id: notification.user_id,
          notification: {
            title: notification.title,
            body: notification.body,
            icon: notification.icon || '/icons/icon-192x192.png',
            badge: notification.badge || '/icons/logo.png',
            data: {
              ...notification.data,
              notification_id: notification.id,
              action_url: notification.action_url
            },
            actions: [
              { action: 'view', title: 'View Details' },
              { action: 'dismiss', title: 'Dismiss' }
            ],
            tag: notification.id,
            requireInteraction: notification.priority === 'urgent'
          }
        }
      });
    } catch (error) {
      console.error('Failed to send push notification:', error);
    }
  };

  const handleMaintenanceAssignment = (task: any) => {
    console.log('Maintenance task assigned:', task);
    // Additional handling for maintenance assignments can be added here
  };

  const handleDamageAssignment = (report: any) => {
    console.log('Damage report assigned:', report);
    // Additional handling for damage assignments can be added here
  };

  const markAsRead = async (notificationId: string) => {
    try {
      const { error } = await supabase
        .from('notifications')
        .update({ 
          is_read: true, 
          read_at: new Date().toISOString() 
        })
        .eq('id', notificationId);

      if (error) throw error;
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
    }
  };

  const markAllAsRead = async () => {
    if (!authState.user?.id) return;

    try {
      const { error } = await supabase
        .from('notifications')
        .update({ 
          is_read: true, 
          read_at: new Date().toISOString() 
        })
        .eq('user_id', authState.user.id)
        .eq('is_read', false);

      if (error) throw error;

      setUnreadCount(0);
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error);
    }
  };

  const deleteNotification = async (notificationId: string) => {
    try {
      const { error } = await supabase
        .from('notifications')
        .delete()
        .eq('id', notificationId);

      if (error) throw error;

      setNotifications(prev => prev.filter(n => n.id !== notificationId));
    } catch (error) {
      console.error('Failed to delete notification:', error);
    }
  };

  return {
    notifications,
    unreadCount,
    preferences,
    loading,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    refreshNotifications: loadInitialData
  };
};
