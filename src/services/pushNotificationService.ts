import { supabase } from '@/integrations/supabase/client';

export interface PushSubscriptionData {
  id?: string;
  user_id: string;
  team_id: string;
  endpoint: string;
  p256dh_key: string;
  auth_key: string;
  user_agent?: string;
  device_type: 'desktop' | 'mobile' | 'tablet';
  is_active?: boolean;
}

export class PushNotificationService {
  // VAPID public key - this should be set via environment variable in production
  private vapidPublicKey = import.meta.env.VITE_VAPID_PUBLIC_KEY || 'BG846qw5AxggJ0QueOzfzJ6NWOI1Q5yHtEn9y9DDzt-O71tISC4n5IkRYEacTV018Y8mXO1tTE_02epYLMAukvk';

  /**
   * Check if the browser supports push notifications
   */
  static isSupported(): boolean {
    return (
      'serviceWorker' in navigator &&
      'PushManager' in window &&
      'Notification' in window
    );
  }

  /**
   * Request notification permission from the user
   */
  async requestPermission(): Promise<NotificationPermission> {
    if (!PushNotificationService.isSupported()) {
      throw new Error('Push notifications are not supported in this browser');
    }

    if (!('Notification' in window)) {
      throw new Error('Browser does not support notifications');
    }

    const permission = await Notification.requestPermission();
    console.log('Notification permission:', permission);
    return permission;
  }

  /**
   * Subscribe to push notifications
   */
  async subscribeToPush(userId: string, teamId: string): Promise<PushSubscription> {
    if (!PushNotificationService.isSupported()) {
      throw new Error('Push notifications are not supported');
    }

    // Check permission first
    const permission = await this.requestPermission();
    if (permission !== 'granted') {
      throw new Error('Notification permission denied');
    }

    try {
      // Wait for service worker to be ready
      const registration = await navigator.serviceWorker.ready;
      
      // Check if already subscribed
      let subscription = await registration.pushManager.getSubscription();
      
      if (!subscription) {
        // Create new subscription
        subscription = await registration.pushManager.subscribe({
          userVisibleOnly: true,
          applicationServerKey: this.urlBase64ToUint8Array(this.vapidPublicKey)
        });
      }

      // Save subscription to database
      await this.saveSubscription(userId, teamId, subscription);
      
      console.log('Push subscription created:', subscription);
      return subscription;
    } catch (error) {
      console.error('Failed to subscribe to push notifications:', error);
      throw error;
    }
  }

  /**
   * Unsubscribe from push notifications
   */
  async unsubscribeFromPush(userId: string): Promise<void> {
    try {
      const registration = await navigator.serviceWorker.ready;
      const subscription = await registration.pushManager.getSubscription();
      
      if (subscription) {
        await subscription.unsubscribe();
        console.log('Unsubscribed from push notifications');
      }

      // Remove from database
      await this.removeSubscription(userId);
    } catch (error) {
      console.error('Failed to unsubscribe from push notifications:', error);
      throw error;
    }
  }

  /**
   * Get current push subscription
   */
  async getCurrentSubscription(): Promise<PushSubscription | null> {
    if (!PushNotificationService.isSupported()) {
      return null;
    }

    try {
      const registration = await navigator.serviceWorker.ready;
      return await registration.pushManager.getSubscription();
    } catch (error) {
      console.error('Failed to get current subscription:', error);
      return null;
    }
  }

  /**
   * Save subscription to database
   */
  async saveSubscription(userId: string, teamId: string, subscription: PushSubscription): Promise<void> {
    const subscriptionData: Omit<PushSubscriptionData, 'id'> = {
      user_id: userId,
      team_id: teamId,
      endpoint: subscription.endpoint,
      p256dh_key: subscription.toJSON().keys?.p256dh || '',
      auth_key: subscription.toJSON().keys?.auth || '',
      user_agent: navigator.userAgent,
      device_type: this.getDeviceType(),
      is_active: true
    };

    const { error } = await supabase
      .from('push_subscriptions')
      .upsert(subscriptionData, {
        onConflict: 'user_id,endpoint'
      });

    if (error) {
      console.error('Failed to save push subscription:', error);
      throw error;
    }

    console.log('Push subscription saved to database');
  }

  /**
   * Remove subscription from database
   */
  async removeSubscription(userId: string): Promise<void> {
    const { error } = await supabase
      .from('push_subscriptions')
      .update({ is_active: false })
      .eq('user_id', userId);

    if (error) {
      console.error('Failed to remove push subscription:', error);
      throw error;
    }

    console.log('Push subscription removed from database');
  }

  /**
   * Get user's push subscriptions from database
   */
  async getUserSubscriptions(userId: string): Promise<PushSubscriptionData[]> {
    const { data, error } = await supabase
      .from('push_subscriptions')
      .select('*')
      .eq('user_id', userId)
      .eq('is_active', true);

    if (error) {
      console.error('Failed to get user subscriptions:', error);
      throw error;
    }

    return data || [];
  }

  /**
   * Detect device type based on user agent
   */
  private getDeviceType(): 'desktop' | 'mobile' | 'tablet' {
    const userAgent = navigator.userAgent.toLowerCase();
    
    if (/tablet|ipad|playbook|silk/i.test(userAgent)) {
      return 'tablet';
    }
    
    if (/mobile|iphone|ipod|android|blackberry|opera|mini|windows\sce|palm|smartphone|iemobile/i.test(userAgent)) {
      return 'mobile';
    }
    
    return 'desktop';
  }

  /**
   * Convert VAPID key from base64 to Uint8Array
   */
  private urlBase64ToUint8Array(base64String: string): Uint8Array {
    const padding = '='.repeat((4 - base64String.length % 4) % 4);
    const base64 = (base64String + padding)
      .replace(/-/g, '+')
      .replace(/_/g, '/');

    const rawData = window.atob(base64);
    const outputArray = new Uint8Array(rawData.length);

    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i);
    }
    return outputArray;
  }

  /**
   * Test if push notifications are working
   */
  async testNotification(title: string = 'Test Notification', body: string = 'This is a test notification from StayFu'): Promise<void> {
    if (!PushNotificationService.isSupported()) {
      throw new Error('Push notifications are not supported');
    }

    const permission = Notification.permission;
    if (permission !== 'granted') {
      throw new Error('Notification permission not granted');
    }

    // Show a local notification for testing
    new Notification(title, {
      body,
      icon: '/icons/icon-192x192.png',
      badge: '/icons/logo.png',
      tag: 'test-notification'
    });
  }
}
