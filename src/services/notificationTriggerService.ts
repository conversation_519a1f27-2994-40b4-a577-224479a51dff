import { supabase } from '@/integrations/supabase/client';

export interface NotificationTriggerData {
  user_id: string;
  team_id: string;
  type: 'maintenance_assigned' | 'damage_assigned' | 'task_due' | 'task_completed' | 'inventory_low' | 'system_notification';
  title: string;
  body: string;
  data?: any;
  icon?: string;
  badge?: string;
  action_url?: string;
  priority?: 'low' | 'normal' | 'high' | 'urgent';
  expires_at?: string;
}

export class NotificationTriggerService {
  /**
   * Create a notification in the database
   */
  static async createNotification(notificationData: NotificationTriggerData): Promise<void> {
    try {
      const { error } = await supabase
        .from('notifications')
        .insert({
          ...notificationData,
          delivery_status: 'pending',
          created_at: new Date().toISOString()
        });

      if (error) {
        console.error('Failed to create notification:', error);
        throw error;
      }

      console.log('Notification created successfully:', notificationData.type);
    } catch (error) {
      console.error('Error creating notification:', error);
      throw error;
    }
  }

  /**
   * Trigger notification for maintenance task assignment
   */
  static async triggerMaintenanceAssignment(
    assignedUserId: string,
    teamId: string,
    taskId: string,
    taskTitle: string,
    propertyName: string,
    priority: 'low' | 'normal' | 'high' | 'urgent' = 'normal'
  ): Promise<void> {
    const notification: NotificationTriggerData = {
      user_id: assignedUserId,
      team_id: teamId,
      type: 'maintenance_assigned',
      title: 'New Maintenance Task Assigned',
      body: `You have been assigned a maintenance task: ${taskTitle} at ${propertyName}`,
      data: {
        task_id: taskId,
        task_title: taskTitle,
        property_name: propertyName,
        task_type: 'maintenance'
      },
      icon: '/icons/maintenance.png',
      badge: '/icons/logo.png',
      action_url: `/maintenance/${taskId}`,
      priority,
      expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7 days
    };

    await this.createNotification(notification);
  }

  /**
   * Trigger notification for damage report assignment
   */
  static async triggerDamageAssignment(
    assignedUserId: string,
    teamId: string,
    reportId: string,
    reportTitle: string,
    propertyName: string,
    priority: 'low' | 'normal' | 'high' | 'urgent' = 'normal'
  ): Promise<void> {
    const notification: NotificationTriggerData = {
      user_id: assignedUserId,
      team_id: teamId,
      type: 'damage_assigned',
      title: 'New Damage Report Assigned',
      body: `You have been assigned a damage report: ${reportTitle} at ${propertyName}`,
      data: {
        report_id: reportId,
        report_title: reportTitle,
        property_name: propertyName,
        task_type: 'damage'
      },
      icon: '/icons/damage.png',
      badge: '/icons/logo.png',
      action_url: `/damage-reports/${reportId}`,
      priority,
      expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7 days
    };

    await this.createNotification(notification);
  }

  /**
   * Trigger notification for task due reminder
   */
  static async triggerTaskDueReminder(
    userId: string,
    teamId: string,
    taskId: string,
    taskTitle: string,
    taskType: 'maintenance' | 'damage',
    dueDate: string,
    propertyName: string
  ): Promise<void> {
    const dueDateObj = new Date(dueDate);
    const now = new Date();
    const hoursUntilDue = Math.ceil((dueDateObj.getTime() - now.getTime()) / (1000 * 60 * 60));
    
    let priority: 'low' | 'normal' | 'high' | 'urgent' = 'normal';
    let bodyText = `Task "${taskTitle}" at ${propertyName} is due in ${hoursUntilDue} hours`;
    
    if (hoursUntilDue <= 2) {
      priority = 'urgent';
      bodyText = `URGENT: Task "${taskTitle}" at ${propertyName} is due in ${hoursUntilDue} hours`;
    } else if (hoursUntilDue <= 24) {
      priority = 'high';
      bodyText = `Task "${taskTitle}" at ${propertyName} is due today`;
    }

    const notification: NotificationTriggerData = {
      user_id: userId,
      team_id: teamId,
      type: 'task_due',
      title: 'Task Due Reminder',
      body: bodyText,
      data: {
        task_id: taskId,
        task_title: taskTitle,
        task_type: taskType,
        property_name: propertyName,
        due_date: dueDate,
        hours_until_due: hoursUntilDue
      },
      icon: '/icons/clock.png',
      badge: '/icons/logo.png',
      action_url: taskType === 'maintenance' ? `/maintenance/${taskId}` : `/damage-reports/${taskId}`,
      priority,
      expires_at: dueDateObj.toISOString()
    };

    await this.createNotification(notification);
  }

  /**
   * Trigger notification for task completion
   */
  static async triggerTaskCompletion(
    userId: string,
    teamId: string,
    taskId: string,
    taskTitle: string,
    taskType: 'maintenance' | 'damage',
    propertyName: string,
    completedBy: string
  ): Promise<void> {
    const notification: NotificationTriggerData = {
      user_id: userId,
      team_id: teamId,
      type: 'task_completed',
      title: 'Task Completed',
      body: `Task "${taskTitle}" at ${propertyName} has been completed by ${completedBy}`,
      data: {
        task_id: taskId,
        task_title: taskTitle,
        task_type: taskType,
        property_name: propertyName,
        completed_by: completedBy
      },
      icon: '/icons/check.png',
      badge: '/icons/logo.png',
      action_url: taskType === 'maintenance' ? `/maintenance/${taskId}` : `/damage-reports/${taskId}`,
      priority: 'low',
      expires_at: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString() // 3 days
    };

    await this.createNotification(notification);
  }

  /**
   * Trigger notification for low inventory
   */
  static async triggerInventoryLowAlert(
    userId: string,
    teamId: string,
    itemName: string,
    currentStock: number,
    minimumStock: number,
    propertyName?: string
  ): Promise<void> {
    const notification: NotificationTriggerData = {
      user_id: userId,
      team_id: teamId,
      type: 'inventory_low',
      title: 'Low Inventory Alert',
      body: `${itemName} is running low (${currentStock} remaining, minimum: ${minimumStock})${propertyName ? ` at ${propertyName}` : ''}`,
      data: {
        item_name: itemName,
        current_stock: currentStock,
        minimum_stock: minimumStock,
        property_name: propertyName
      },
      icon: '/icons/inventory.png',
      badge: '/icons/logo.png',
      action_url: '/inventory',
      priority: currentStock === 0 ? 'urgent' : currentStock <= minimumStock / 2 ? 'high' : 'normal',
      expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7 days
    };

    await this.createNotification(notification);
  }

  /**
   * Trigger system notification
   */
  static async triggerSystemNotification(
    userId: string,
    teamId: string,
    title: string,
    body: string,
    actionUrl?: string,
    priority: 'low' | 'normal' | 'high' | 'urgent' = 'normal',
    expiresInHours: number = 24
  ): Promise<void> {
    const notification: NotificationTriggerData = {
      user_id: userId,
      team_id: teamId,
      type: 'system_notification',
      title,
      body,
      data: {
        system_notification: true
      },
      icon: '/icons/system.png',
      badge: '/icons/logo.png',
      action_url: actionUrl,
      priority,
      expires_at: new Date(Date.now() + expiresInHours * 60 * 60 * 1000).toISOString()
    };

    await this.createNotification(notification);
  }

  /**
   * Bulk create notifications for multiple users
   */
  static async createBulkNotifications(notifications: NotificationTriggerData[]): Promise<void> {
    try {
      const notificationsWithDefaults = notifications.map(notification => ({
        ...notification,
        delivery_status: 'pending',
        created_at: new Date().toISOString()
      }));

      const { error } = await supabase
        .from('notifications')
        .insert(notificationsWithDefaults);

      if (error) {
        console.error('Failed to create bulk notifications:', error);
        throw error;
      }

      console.log(`${notifications.length} notifications created successfully`);
    } catch (error) {
      console.error('Error creating bulk notifications:', error);
      throw error;
    }
  }

  /**
   * Get user's notification preferences to check if notification should be sent
   */
  static async shouldSendNotification(
    userId: string,
    notificationType: 'maintenance_assigned' | 'damage_assigned' | 'task_due' | 'task_completed' | 'inventory_low'
  ): Promise<{ push: boolean; email: boolean; desktop: boolean }> {
    try {
      const { data, error } = await supabase
        .from('user_settings')
        .select('notification_preferences')
        .eq('user_id', userId)
        .single();

      if (error || !data?.notification_preferences) {
        // Return default preferences if not found
        return { push: true, email: true, desktop: true };
      }

      const preferences = data.notification_preferences[notificationType];
      return preferences || { push: true, email: true, desktop: true };
    } catch (error) {
      console.error('Failed to get notification preferences:', error);
      // Return default preferences on error
      return { push: true, email: true, desktop: true };
    }
  }

  /**
   * Check if user is in quiet hours
   */
  static async isInQuietHours(userId: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('user_settings')
        .select('notification_preferences')
        .eq('user_id', userId)
        .single();

      if (error || !data?.notification_preferences?.quiet_hours?.enabled) {
        return false;
      }

      const quietHours = data.notification_preferences.quiet_hours;
      const now = new Date();
      const currentTime = now.getHours() * 100 + now.getMinutes();
      const startTime = parseInt(quietHours.start.replace(':', ''));
      const endTime = parseInt(quietHours.end.replace(':', ''));

      if (startTime > endTime) {
        // Quiet hours span midnight
        return currentTime >= startTime || currentTime <= endTime;
      } else {
        // Normal quiet hours
        return currentTime >= startTime && currentTime <= endTime;
      }
    } catch (error) {
      console.error('Failed to check quiet hours:', error);
      return false;
    }
  }
}
