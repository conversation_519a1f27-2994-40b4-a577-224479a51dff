const { chromium } = require('playwright');

(async () => {
  let browser, context, page;
  
  try {
    console.log('Starting quick glass styling test...');
    
    browser = await chromium.launch({ 
      headless: false,
      args: ['--no-sandbox', '--disable-dev-shm-usage']
    });
    
    context = await browser.newContext({
      viewport: { width: 1280, height: 720 }
    });
    
    page = await context.newPage();
    
    // Wait for dev server
    console.log('Waiting for dev server...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    console.log('Navigating to dashboard...');
    await page.goto('http://localhost:8080/#/dashboard', { 
      waitUntil: 'networkidle',
      timeout: 30000 
    });
    
    // Check if the page loaded
    await page.waitForSelector('body', { timeout: 10000 });
    
    // Take screenshot of dashboard
    console.log('Taking dashboard screenshot...');
    await page.screenshot({ 
      path: 'dashboard-glass-test.png',
      fullPage: true 
    });
    
    // Try to navigate to teams page
    console.log('Navigating to teams page...');
    await page.goto('http://localhost:8080/#/teams', { 
      waitUntil: 'networkidle',
      timeout: 30000 
    });
    
    await page.screenshot({ 
      path: 'teams-glass-test.png',
      fullPage: true 
    });
    
    // Try maintenance page  
    console.log('Navigating to maintenance page...');
    await page.goto('http://localhost:8080/#/maintenance', { 
      waitUntil: 'networkidle',
      timeout: 30000 
    });
    
    await page.screenshot({ 
      path: 'maintenance-glass-test.png',
      fullPage: true 
    });
    
    console.log('Screenshots taken successfully!');
    
  } catch (error) {
    console.error('Error during testing:', error);
  } finally {
    if (browser) {
      await browser.close();
    }
    console.log('Test completed.');
  }
})();