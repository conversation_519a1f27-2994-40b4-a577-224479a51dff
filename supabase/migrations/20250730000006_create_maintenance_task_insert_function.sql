-- Create a function to insert maintenance tasks with proper UUID casting
CREATE OR <PERSON><PERSON>LACE FUNCTION insert_maintenance_task(
  p_title TEXT,
  p_description TEXT,
  p_user_id TEXT,
  p_property_id TEXT DEFAULT NULL,
  p_property_name TEXT DEFAULT NULL,
  p_severity TEXT DEFAULT 'medium',
  p_status TEXT DEFAULT 'open',
  p_due_date TEXT DEFAULT NULL,
  p_provider_id TEXT DEFAULT NULL,
  p_provider_email TEXT DEFAULT NULL,
  p_assigned_to TEXT DEFAULT NULL,
  p_team_id TEXT DEFAULT NULL,
  p_is_recurring BOOLEAN DEFAULT FALSE,
  p_recurrence_interval_days INTEGER DEFAULT NULL,
  p_parent_task_id TEXT DEFAULT NULL,
  p_next_due_date TEXT DEFAULT NULL,
  p_recurrence_count INTEGER DEFAULT 0,
  p_max_recurrences INTEGER DEFAULT NULL
)
RETURNS TABLE (
  id UUID,
  title TEXT,
  description TEXT,
  property_id UUID,
  property_name TEXT,
  severity TEXT,
  status TEXT,
  due_date TEXT,
  user_id UUID,
  provider_id UUID,
  provider_email TEXT,
  assigned_to TEXT,
  team_id UUID,
  is_recurring BOOLEAN,
  recurrence_interval_days INTEGER,
  parent_task_id UUID,
  next_due_date TIMESTAMP WITH TIME ZONE,
  recurrence_count INTEGER,
  max_recurrences INTEGER,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE,
  email_notification_sent BOOLEAN,
  email_notification_sent_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  INSERT INTO maintenance_tasks (
    title,
    description,
    property_id,
    property_name,
    severity,
    status,
    due_date,
    user_id,
    provider_id,
    provider_email,
    assigned_to,
    team_id,
    is_recurring,
    recurrence_interval_days,
    parent_task_id,
    next_due_date,
    recurrence_count,
    max_recurrences
  ) VALUES (
    p_title,
    p_description,
    CASE WHEN p_property_id IS NOT NULL AND p_property_id != '' THEN p_property_id::UUID ELSE NULL END,
    p_property_name,
    p_severity,
    p_status,
    p_due_date,
    p_user_id::UUID,
    CASE WHEN p_provider_id IS NOT NULL AND p_provider_id != '' THEN p_provider_id::UUID ELSE NULL END,
    p_provider_email,
    p_assigned_to,
    CASE WHEN p_team_id IS NOT NULL AND p_team_id != '' THEN p_team_id::UUID ELSE NULL END,
    p_is_recurring,
    p_recurrence_interval_days,
    CASE WHEN p_parent_task_id IS NOT NULL AND p_parent_task_id != '' THEN p_parent_task_id::UUID ELSE NULL END,
    CASE WHEN p_next_due_date IS NOT NULL AND p_next_due_date != '' THEN p_next_due_date::TIMESTAMP WITH TIME ZONE ELSE NULL END,
    p_recurrence_count,
    p_max_recurrences
  )
  RETURNING
    maintenance_tasks.id,
    maintenance_tasks.title,
    maintenance_tasks.description,
    maintenance_tasks.property_id,
    maintenance_tasks.property_name,
    maintenance_tasks.severity,
    maintenance_tasks.status,
    maintenance_tasks.due_date,
    maintenance_tasks.user_id,
    maintenance_tasks.provider_id,
    maintenance_tasks.provider_email,
    maintenance_tasks.assigned_to,
    maintenance_tasks.team_id,
    maintenance_tasks.is_recurring,
    maintenance_tasks.recurrence_interval_days,
    maintenance_tasks.parent_task_id,
    maintenance_tasks.next_due_date,
    maintenance_tasks.recurrence_count,
    maintenance_tasks.max_recurrences,
    maintenance_tasks.created_at,
    maintenance_tasks.updated_at,
    maintenance_tasks.completed_at,
    maintenance_tasks.email_notification_sent,
    maintenance_tasks.email_notification_sent_at;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION insert_maintenance_task TO authenticated;
