-- Fix notification triggers to use 'severity' instead of 'priority' for maintenance_tasks
-- The maintenance_tasks table has 'severity' column, not 'priority'

-- Update the maintenance assignment notification function
CREATE OR REPLACE FUNCTION create_maintenance_assignment_notification()
RETURNS TRIGGER AS $$
DECLARE
  property_name TEXT;
  task_title TEXT;
BEGIN
  -- Only create notification if assigned_to has changed and is not null
  IF (TG_OP = 'UPDATE' AND OLD.assigned_to IS DISTINCT FROM NEW.assigned_to AND NEW.assigned_to IS NOT NULL) OR
     (TG_OP = 'INSERT' AND NEW.assigned_to IS NOT NULL) THEN
    
    -- Get property name
    SELECT name INTO property_name
    FROM properties 
    WHERE id = NEW.property_id;
    
    -- Use title or create a default one
    task_title := COALESCE(NEW.title, 'Maintenance Task');
    
    -- Insert notification
    INSERT INTO notifications (
      user_id,
      team_id,
      type,
      title,
      body,
      data,
      icon,
      badge,
      action_url,
      priority,
      expires_at
    ) VALUES (
      NEW.assigned_to,
      NEW.team_id,
      'maintenance_assigned',
      'New Maintenance Task Assigned',
      'You have been assigned a maintenance task: ' || task_title || 
      CASE WHEN property_name IS NOT NULL THEN ' at ' || property_name ELSE '' END,
      jsonb_build_object(
        'task_id', NEW.id,
        'task_title', task_title,
        'property_name', property_name,
        'task_type', 'maintenance',
        'severity', NEW.severity
      ),
      '/icons/maintenance.png',
      '/icons/logo.png',
      '/maintenance/' || NEW.id,
      CASE 
        WHEN NEW.severity = 'critical' THEN 'urgent'
        WHEN NEW.severity = 'high' THEN 'high'
        ELSE 'normal'
      END,
      NOW() + INTERVAL '7 days'
    );
    
    -- Log the notification creation
    RAISE NOTICE 'Created maintenance assignment notification for user % on task %', NEW.assigned_to, NEW.id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;
