-- Fix RLS policy for maintenance_tasks INSERT operations
-- The policy was checking for 'manage_properties' permission but the can_create_maintenance_task function
-- checks for 'create_maintenance_task' or 'manage_maintenance' permissions.
-- This mismatch was causing RLS policy violations even when users had proper permissions.

-- Drop the existing policy
DROP POLICY IF EXISTS "Maintenance tasks are insertable by authorized users" ON "public"."maintenance_tasks";

-- Recreate the policy with correct permission checks that match the can_create_maintenance_task function
CREATE POLICY "Maintenance tasks are insertable by authorized users"
ON "public"."maintenance_tasks"
AS PERMISSIVE
FOR INSERT
TO public
WITH CHECK (
  -- Property owners can create tasks for their properties
  (EXISTS (
    SELECT 1
    FROM properties p
    WHERE p.id = maintenance_tasks.property_id AND p.user_id = auth.uid()
  ))
  OR
  -- Team members with proper permissions can create tasks
  ((team_id IS NOT NULL) AND (EXISTS (
    SELECT 1
    FROM team_members tm
    JOIN user_permissions up ON (tm.user_id = up.user_id AND tm.team_id = up.team_id)
    WHERE tm.team_id = maintenance_tasks.team_id 
      AND tm.user_id = auth.uid() 
      AND tm.status = 'active'
      AND (
        up.permission = 'create_maintenance_task'::permission_type OR
        up.permission = 'manage_maintenance'::permission_type OR
        up.permission = 'manage_properties'::permission_type
      )
      AND up.enabled = true
  )))
  OR
  -- Super admins, admins, and service providers can create tasks
  (EXISTS (
    SELECT 1
    FROM profiles p
    WHERE p.id = auth.uid() 
      AND (
        p.is_super_admin = true OR 
        p.role = 'admin'::user_role OR 
        p.role = 'service_provider'::user_role
      )
  ))
);
