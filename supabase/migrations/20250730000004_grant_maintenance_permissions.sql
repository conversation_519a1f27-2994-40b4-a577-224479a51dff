-- Grant maintenance task permissions to property managers and service providers
-- This ensures that users with property_manager and service_provider roles can create maintenance tasks

DO $$
DECLARE
    user_record RECORD;
    team_record RECORD;
BEGIN
    -- Grant create_maintenance_task permission to all property managers in their teams
    FOR user_record IN 
        SELECT DISTINCT p.id as user_id, tm.team_id
        FROM profiles p
        JOIN team_members tm ON p.id = tm.user_id
        WHERE p.role = 'property_manager' 
        AND tm.status = 'active'
    LOOP
        -- Insert permission if it doesn't exist
        INSERT INTO user_permissions (user_id, team_id, permission, enabled, created_at, updated_at)
        VALUES (
            user_record.user_id, 
            user_record.team_id, 
            'create_maintenance_task'::permission_type, 
            true, 
            NOW(), 
            NOW()
        )
        ON CONFLICT (user_id, team_id, permission) 
        DO UPDATE SET 
            enabled = true,
            updated_at = NOW();
    END LOOP;

    -- Grant manage_maintenance permission to all property managers in their teams
    FOR user_record IN 
        SELECT DISTINCT p.id as user_id, tm.team_id
        FROM profiles p
        JOIN team_members tm ON p.id = tm.user_id
        WHERE p.role = 'property_manager' 
        AND tm.status = 'active'
    LOOP
        -- Insert permission if it doesn't exist
        INSERT INTO user_permissions (user_id, team_id, permission, enabled, created_at, updated_at)
        VALUES (
            user_record.user_id, 
            user_record.team_id, 
            'manage_maintenance'::permission_type, 
            true, 
            NOW(), 
            NOW()
        )
        ON CONFLICT (user_id, team_id, permission) 
        DO UPDATE SET 
            enabled = true,
            updated_at = NOW();
    END LOOP;

    -- Grant create_maintenance_task permission to all service providers in their teams
    FOR user_record IN 
        SELECT DISTINCT p.id as user_id, tm.team_id
        FROM profiles p
        JOIN team_members tm ON p.id = tm.user_id
        WHERE p.role = 'service_provider' 
        AND tm.status = 'active'
    LOOP
        -- Insert permission if it doesn't exist
        INSERT INTO user_permissions (user_id, team_id, permission, enabled, created_at, updated_at)
        VALUES (
            user_record.user_id, 
            user_record.team_id, 
            'create_maintenance_task'::permission_type, 
            true, 
            NOW(), 
            NOW()
        )
        ON CONFLICT (user_id, team_id, permission) 
        DO UPDATE SET 
            enabled = true,
            updated_at = NOW();
    END LOOP;

    RAISE NOTICE 'Maintenance permissions granted successfully';
END $$;
