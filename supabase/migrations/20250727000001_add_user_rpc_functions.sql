-- Add RPC functions for AI command processor to handle team permissions properly
-- These functions replace direct table queries with team-aware queries

-- Drop existing function if it exists to avoid return type conflicts
DROP FUNCTION IF EXISTS get_user_properties(UUID);

-- Function to get user properties (including team properties)
CREATE OR REPLACE FUNCTION get_user_properties(p_user_id UUID)
RETURNS TABLE (
  id UUID,
  name TEXT,
  address TEXT,
  city TEXT,
  state TEXT,
  zip TEXT,
  bedrooms INTEGER,
  bathrooms INTEGER
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    p.id,
    p.name,
    p.address,
    p.city,
    p.state,
    p.zip,
    p.bedrooms,
    p.bathrooms
  FROM properties p
  LEFT JOIN team_properties tp ON p.id = tp.property_id
  LEFT JOIN team_members tm ON tp.team_id = tm.team_id
  WHERE p.user_id = p_user_id 
     OR tm.user_id = p_user_id;
END;
$$;

-- Drop existing function if it exists to avoid return type conflicts
DROP FUNCTION IF EXISTS get_user_inventory_items(UUID);

-- Function to get user inventory items (including team inventory)
CREATE OR REPLACE FUNCTION get_user_inventory_items(p_user_id UUID)
RETURNS TABLE (
  id UUID,
  name TEXT,
  quantity INTEGER,
  min_quantity INTEGER,
  property_name TEXT,
  collection_name TEXT,
  price DECIMAL,
  image_url TEXT
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    i.id,
    i.name,
    i.quantity,
    i.min_quantity,
    p.name as property_name,
    i.collection as collection_name,
    i.price,
    i.image_url
  FROM inventory_items i
  LEFT JOIN properties p ON i.property_id = p.id
  LEFT JOIN team_properties tp ON p.id = tp.property_id
  LEFT JOIN team_members tm ON tp.team_id = tm.team_id
  WHERE i.user_id = p_user_id 
     OR tm.user_id = p_user_id;
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION get_user_properties(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_inventory_items(UUID) TO authenticated;
