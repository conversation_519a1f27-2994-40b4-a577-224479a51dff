-- Database triggers for automatic notification creation
-- This migration creates PostgreSQL functions and triggers for automatic notification generation

-- Function to create maintenance assignment notification
CREATE OR REPLACE FUNCTION create_maintenance_assignment_notification()
RETURNS TRIGGER AS $$
DECLARE
  property_name TEXT;
  task_title TEXT;
BEGIN
  -- Only create notification if assigned_to has changed and is not null
  IF (TG_OP = 'UPDATE' AND OLD.assigned_to IS DISTINCT FROM NEW.assigned_to AND NEW.assigned_to IS NOT NULL) OR
     (TG_OP = 'INSERT' AND NEW.assigned_to IS NOT NULL) THEN
    
    -- Get property name
    SELECT name INTO property_name
    FROM properties 
    WHERE id = NEW.property_id;
    
    -- Use title or create a default one
    task_title := COALESCE(NEW.title, 'Maintenance Task');
    
    -- Insert notification
    INSERT INTO notifications (
      user_id,
      team_id,
      type,
      title,
      body,
      data,
      icon,
      badge,
      action_url,
      priority,
      expires_at
    ) VALUES (
      NEW.assigned_to,
      NEW.team_id,
      'maintenance_assigned',
      'New Maintenance Task Assigned',
      'You have been assigned a maintenance task: ' || task_title || 
      CASE WHEN property_name IS NOT NULL THEN ' at ' || property_name ELSE '' END,
      jsonb_build_object(
        'task_id', NEW.id,
        'task_title', task_title,
        'property_name', property_name,
        'task_type', 'maintenance',
        'priority', NEW.priority
      ),
      '/icons/maintenance.png',
      '/icons/logo.png',
      '/maintenance/' || NEW.id,
      CASE 
        WHEN NEW.priority = 'urgent' THEN 'urgent'
        WHEN NEW.priority = 'high' THEN 'high'
        ELSE 'normal'
      END,
      NOW() + INTERVAL '7 days'
    );
    
    -- Log the notification creation
    RAISE NOTICE 'Created maintenance assignment notification for user % on task %', NEW.assigned_to, NEW.id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to create damage assignment notification
CREATE OR REPLACE FUNCTION create_damage_assignment_notification()
RETURNS TRIGGER AS $$
DECLARE
  property_name TEXT;
  report_title TEXT;
BEGIN
  -- Only create notification if provider_id has changed and is not null
  IF (TG_OP = 'UPDATE' AND OLD.provider_id IS DISTINCT FROM NEW.provider_id AND NEW.provider_id IS NOT NULL) OR
     (TG_OP = 'INSERT' AND NEW.provider_id IS NOT NULL) THEN
    
    -- Get property name
    SELECT name INTO property_name
    FROM properties 
    WHERE id = NEW.property_id;
    
    -- Use title or create a default one
    report_title := COALESCE(NEW.title, 'Damage Report');
    
    -- Insert notification
    INSERT INTO notifications (
      user_id,
      team_id,
      type,
      title,
      body,
      data,
      icon,
      badge,
      action_url,
      priority,
      expires_at
    ) VALUES (
      NEW.provider_id,
      NEW.team_id,
      'damage_assigned',
      'New Damage Report Assigned',
      'You have been assigned a damage report: ' || report_title || 
      CASE WHEN property_name IS NOT NULL THEN ' at ' || property_name ELSE '' END,
      jsonb_build_object(
        'report_id', NEW.id,
        'report_title', report_title,
        'property_name', property_name,
        'task_type', 'damage',
        'priority', NEW.priority
      ),
      '/icons/damage.png',
      '/icons/logo.png',
      '/damage-reports/' || NEW.id,
      CASE 
        WHEN NEW.priority = 'urgent' THEN 'urgent'
        WHEN NEW.priority = 'high' THEN 'high'
        ELSE 'normal'
      END,
      NOW() + INTERVAL '7 days'
    );
    
    -- Log the notification creation
    RAISE NOTICE 'Created damage assignment notification for user % on report %', NEW.provider_id, NEW.id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to create task completion notification
CREATE OR REPLACE FUNCTION create_task_completion_notification()
RETURNS TRIGGER AS $$
DECLARE
  property_name TEXT;
  task_title TEXT;
  completed_by_name TEXT;
  task_creator_id UUID;
BEGIN
  -- Only create notification if status changed to completed
  IF TG_OP = 'UPDATE' AND OLD.status != 'completed' AND NEW.status = 'completed' THEN
    
    -- Get property name
    SELECT name INTO property_name
    FROM properties 
    WHERE id = NEW.property_id;
    
    -- Get completed by user name
    SELECT COALESCE(full_name, email) INTO completed_by_name
    FROM auth.users 
    WHERE id = NEW.assigned_to;
    
    -- Use title or create a default one
    task_title := COALESCE(NEW.title, 'Maintenance Task');
    
    -- Get task creator (could be from created_by field if it exists)
    -- For now, we'll notify team members or property managers
    -- This is a simplified approach - you might want to track who created the task
    
    -- Insert notification for team members (excluding the person who completed it)
    INSERT INTO notifications (
      user_id,
      team_id,
      type,
      title,
      body,
      data,
      icon,
      badge,
      action_url,
      priority,
      expires_at
    )
    SELECT 
      tm.user_id,
      NEW.team_id,
      'task_completed',
      'Task Completed',
      'Task "' || task_title || '"' || 
      CASE WHEN property_name IS NOT NULL THEN ' at ' || property_name ELSE '' END ||
      ' has been completed by ' || COALESCE(completed_by_name, 'Unknown'),
      jsonb_build_object(
        'task_id', NEW.id,
        'task_title', task_title,
        'property_name', property_name,
        'task_type', 'maintenance',
        'completed_by', completed_by_name
      ),
      '/icons/check.png',
      '/icons/logo.png',
      '/maintenance/' || NEW.id,
      'low',
      NOW() + INTERVAL '3 days'
    FROM team_members tm
    WHERE tm.team_id = NEW.team_id 
      AND tm.user_id != NEW.assigned_to  -- Don't notify the person who completed it
      AND tm.role IN ('admin', 'manager'); -- Only notify admins and managers
    
    -- Log the notification creation
    RAISE NOTICE 'Created task completion notifications for task %', NEW.id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to create damage completion notification
CREATE OR REPLACE FUNCTION create_damage_completion_notification()
RETURNS TRIGGER AS $$
DECLARE
  property_name TEXT;
  report_title TEXT;
  completed_by_name TEXT;
BEGIN
  -- Only create notification if status changed to completed
  IF TG_OP = 'UPDATE' AND OLD.status != 'completed' AND NEW.status = 'completed' THEN
    
    -- Get property name
    SELECT name INTO property_name
    FROM properties 
    WHERE id = NEW.property_id;
    
    -- Get completed by user name
    SELECT COALESCE(full_name, email) INTO completed_by_name
    FROM auth.users 
    WHERE id = NEW.provider_id;
    
    -- Use title or create a default one
    report_title := COALESCE(NEW.title, 'Damage Report');
    
    -- Insert notification for team members (excluding the person who completed it)
    INSERT INTO notifications (
      user_id,
      team_id,
      type,
      title,
      body,
      data,
      icon,
      badge,
      action_url,
      priority,
      expires_at
    )
    SELECT 
      tm.user_id,
      NEW.team_id,
      'task_completed',
      'Damage Report Completed',
      'Damage report "' || report_title || '"' || 
      CASE WHEN property_name IS NOT NULL THEN ' at ' || property_name ELSE '' END ||
      ' has been completed by ' || COALESCE(completed_by_name, 'Unknown'),
      jsonb_build_object(
        'report_id', NEW.id,
        'report_title', report_title,
        'property_name', property_name,
        'task_type', 'damage',
        'completed_by', completed_by_name
      ),
      '/icons/check.png',
      '/icons/logo.png',
      '/damage-reports/' || NEW.id,
      'low',
      NOW() + INTERVAL '3 days'
    FROM team_members tm
    WHERE tm.team_id = NEW.team_id 
      AND tm.user_id != NEW.provider_id  -- Don't notify the person who completed it
      AND tm.role IN ('admin', 'manager'); -- Only notify admins and managers
    
    -- Log the notification creation
    RAISE NOTICE 'Created damage completion notifications for report %', NEW.id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for maintenance tasks
CREATE TRIGGER maintenance_assignment_notification_trigger
  AFTER INSERT OR UPDATE ON maintenance_tasks
  FOR EACH ROW
  EXECUTE FUNCTION create_maintenance_assignment_notification();

CREATE TRIGGER maintenance_completion_notification_trigger
  AFTER UPDATE ON maintenance_tasks
  FOR EACH ROW
  EXECUTE FUNCTION create_task_completion_notification();

-- Create triggers for damage reports
CREATE TRIGGER damage_assignment_notification_trigger
  AFTER INSERT OR UPDATE ON damage_reports
  FOR EACH ROW
  EXECUTE FUNCTION create_damage_assignment_notification();

CREATE TRIGGER damage_completion_notification_trigger
  AFTER UPDATE ON damage_reports
  FOR EACH ROW
  EXECUTE FUNCTION create_damage_completion_notification();

-- Function to create due date reminder notifications (to be called by a scheduled job)
CREATE OR REPLACE FUNCTION create_due_date_reminders()
RETURNS void AS $$
BEGIN
  -- Create reminders for maintenance tasks due in 24 hours
  INSERT INTO notifications (
    user_id,
    team_id,
    type,
    title,
    body,
    data,
    icon,
    badge,
    action_url,
    priority,
    expires_at
  )
  SELECT 
    mt.assigned_to,
    mt.team_id,
    'task_due',
    'Task Due Reminder',
    'Task "' || COALESCE(mt.title, 'Maintenance Task') || '"' ||
    CASE WHEN p.name IS NOT NULL THEN ' at ' || p.name ELSE '' END ||
    ' is due in 24 hours',
    jsonb_build_object(
      'task_id', mt.id,
      'task_title', COALESCE(mt.title, 'Maintenance Task'),
      'property_name', p.name,
      'task_type', 'maintenance',
      'due_date', mt.due_date,
      'hours_until_due', 24
    ),
    '/icons/clock.png',
    '/icons/logo.png',
    '/maintenance/' || mt.id,
    'high',
    mt.due_date
  FROM maintenance_tasks mt
  LEFT JOIN properties p ON p.id = mt.property_id
  WHERE mt.assigned_to IS NOT NULL
    AND mt.status != 'completed'
    AND mt.due_date IS NOT NULL
    AND mt.due_date BETWEEN NOW() + INTERVAL '23 hours' AND NOW() + INTERVAL '25 hours'
    AND NOT EXISTS (
      -- Don't create duplicate reminders
      SELECT 1 FROM notifications n 
      WHERE n.user_id = mt.assigned_to 
        AND n.type = 'task_due'
        AND n.data->>'task_id' = mt.id::text
        AND n.created_at > NOW() - INTERVAL '12 hours'
    );

  -- Create reminders for damage reports due in 24 hours
  INSERT INTO notifications (
    user_id,
    team_id,
    type,
    title,
    body,
    data,
    icon,
    badge,
    action_url,
    priority,
    expires_at
  )
  SELECT 
    dr.provider_id,
    dr.team_id,
    'task_due',
    'Task Due Reminder',
    'Damage report "' || COALESCE(dr.title, 'Damage Report') || '"' ||
    CASE WHEN p.name IS NOT NULL THEN ' at ' || p.name ELSE '' END ||
    ' is due in 24 hours',
    jsonb_build_object(
      'report_id', dr.id,
      'report_title', COALESCE(dr.title, 'Damage Report'),
      'property_name', p.name,
      'task_type', 'damage',
      'due_date', dr.due_date,
      'hours_until_due', 24
    ),
    '/icons/clock.png',
    '/icons/logo.png',
    '/damage-reports/' || dr.id,
    'high',
    dr.due_date
  FROM damage_reports dr
  LEFT JOIN properties p ON p.id = dr.property_id
  WHERE dr.provider_id IS NOT NULL
    AND dr.status != 'completed'
    AND dr.due_date IS NOT NULL
    AND dr.due_date BETWEEN NOW() + INTERVAL '23 hours' AND NOW() + INTERVAL '25 hours'
    AND NOT EXISTS (
      -- Don't create duplicate reminders
      SELECT 1 FROM notifications n 
      WHERE n.user_id = dr.provider_id 
        AND n.type = 'task_due'
        AND n.data->>'report_id' = dr.id::text
        AND n.created_at > NOW() - INTERVAL '12 hours'
    );

  RAISE NOTICE 'Created due date reminder notifications';
END;
$$ LANGUAGE plpgsql;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION create_maintenance_assignment_notification() TO authenticated;
GRANT EXECUTE ON FUNCTION create_damage_assignment_notification() TO authenticated;
GRANT EXECUTE ON FUNCTION create_task_completion_notification() TO authenticated;
GRANT EXECUTE ON FUNCTION create_damage_completion_notification() TO authenticated;
GRANT EXECUTE ON FUNCTION create_due_date_reminders() TO authenticated;
