import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Get the authorization header
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Missing authorization header' }),
        { 
          status: 401, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Verify the user token
    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabaseClient.auth.getUser(token)

    if (authError || !user) {
      return new Response(
        JSON.stringify({ error: 'Invalid or expired token' }),
        { 
          status: 401, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Get request body
    const body = await req.json()
    const { notification_id, notification_ids } = body

    if (!notification_id && !notification_ids) {
      return new Response(
        JSON.stringify({ error: 'Missing notification_id or notification_ids' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    const currentTime = new Date().toISOString()

    if (notification_ids && Array.isArray(notification_ids)) {
      // Mark multiple notifications as read
      const { data, error } = await supabaseClient
        .from('notifications')
        .update({ 
          is_read: true, 
          read_at: currentTime 
        })
        .in('id', notification_ids)
        .eq('user_id', user.id)
        .select('id')

      if (error) {
        console.error('Error marking notifications as read:', error)
        return new Response(
          JSON.stringify({ error: 'Failed to mark notifications as read' }),
          { 
            status: 500, 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
          }
        )
      }

      return new Response(
        JSON.stringify({ 
          message: 'Notifications marked as read successfully',
          updated_count: data?.length || 0,
          updated_ids: data?.map(n => n.id) || []
        }),
        { 
          status: 200, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    } else {
      // Mark single notification as read
      const { data, error } = await supabaseClient
        .from('notifications')
        .update({ 
          is_read: true, 
          read_at: currentTime 
        })
        .eq('id', notification_id)
        .eq('user_id', user.id)
        .select('id, title')
        .single()

      if (error) {
        console.error('Error marking notification as read:', error)
        return new Response(
          JSON.stringify({ error: 'Failed to mark notification as read' }),
          { 
            status: 500, 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
          }
        )
      }

      if (!data) {
        return new Response(
          JSON.stringify({ error: 'Notification not found or access denied' }),
          { 
            status: 404, 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
          }
        )
      }

      return new Response(
        JSON.stringify({ 
          message: 'Notification marked as read successfully',
          notification: data
        }),
        { 
          status: 200, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

  } catch (error) {
    console.error('Error in mark-notification-read function:', error)
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
