// Intelligent suggestions and matching functions for AI command processor

export async function generateIntelligentSuggestions(
  aiPrompt: string,
  userCommand: string,
  supabase: any,
  userId: string
) {
  // This function can be enhanced to provide intelligent suggestions
  // based on user's command context and data
  return [];
}

export async function findSimilarItems(
  supabase: any,
  userId: string,
  itemName: string,
  propertyId?: string
) {
  try {
    // Get all user inventory items
    const { data: allItems } = await supabase.rpc('get_user_inventory_items', {
      p_user_id: userId
    });

    if (!allItems || allItems.length === 0) {
      return [];
    }

    // Find items with similar names (case-insensitive)
    const normalizedItemName = itemName.toLowerCase().trim();
    const similarItems = allItems.filter(item => {
      const itemNameLower = item.name.toLowerCase();
      
      // Exact match
      if (itemNameLower === normalizedItemName) {
        return true;
      }
      
      // Contains match
      if (itemNameLower.includes(normalizedItemName) || normalizedItemName.includes(itemNameLower)) {
        return true;
      }
      
      // Word-based matching
      const itemWords = itemNameLower.split(/\s+/);
      const searchWords = normalizedItemName.split(/\s+/);
      
      // Check if any search words match any item words
      const hasWordMatch = searchWords.some(searchWord => 
        itemWords.some(itemWord => 
          itemWord.includes(searchWord) || searchWord.includes(itemWord)
        )
      );
      
      return hasWordMatch;
    });

    // Filter out items from the target property if specified
    return propertyId ? 
      similarItems.filter(item => item.property_id !== propertyId) :
      similarItems;

  } catch (error) {
    console.error('[findSimilarItems] Error:', error);
    return [];
  }
}

export function suggestProperties(properties: any[]) {
  return properties.map(p => p.name).slice(0, 5);
}

// Smart property matching function that handles common user phrases
export function findBestPropertyMatch(properties: any[], userQuery: string): any | null {
  if (!properties || properties.length === 0 || !userQuery) {
    return null;
  }

  const query = userQuery.toLowerCase().trim();

  // Direct exact match first
  let exactMatch = properties.find(p => p.name.toLowerCase() === query);
  if (exactMatch) return exactMatch;

  // Handle common phrases like "true property" -> look for properties containing "true"
  const queryWords = query.split(/\s+/);

  // If user says "X property", look for properties containing "X"
  if (queryWords.length === 2 && queryWords[1] === 'property') {
    const searchTerm = queryWords[0];
    const matches = properties.filter(p =>
      p.name.toLowerCase().includes(searchTerm)
    );
    if (matches.length === 1) return matches[0];
    if (matches.length > 1) {
      // Prefer shorter names (more specific)
      return matches.sort((a, b) => a.name.length - b.name.length)[0];
    }
  }

  // Partial matching - find properties that contain any of the query words
  const partialMatches = properties.filter(p => {
    const propName = p.name.toLowerCase();
    return queryWords.some(word => propName.includes(word));
  });

  if (partialMatches.length === 1) return partialMatches[0];
  if (partialMatches.length > 1) {
    // Prefer properties that match more words
    const scored = partialMatches.map(p => {
      const propName = p.name.toLowerCase();
      const matchCount = queryWords.filter(word => propName.includes(word)).length;
      return { property: p, score: matchCount };
    });

    scored.sort((a, b) => {
      if (a.score !== b.score) return b.score - a.score; // Higher score first
      return a.property.name.length - b.property.name.length; // Shorter name first
    });

    return scored[0].property;
  }

  return null;
}
