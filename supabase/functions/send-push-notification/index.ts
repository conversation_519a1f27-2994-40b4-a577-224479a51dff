import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface PushSubscription {
  endpoint: string;
  p256dh_key: string;
  auth_key: string;
}

interface NotificationPayload {
  title: string;
  body: string;
  icon?: string;
  badge?: string;
  data?: any;
  actions?: Array<{ action: string; title: string; icon?: string }>;
  tag?: string;
  requireInteraction?: boolean;
  priority?: 'low' | 'normal' | 'high' | 'urgent';
  action_url?: string;
  notification_id?: string;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Get request body
    const { user_id, notification } = await req.json()

    if (!user_id || !notification) {
      return new Response(
        JSON.stringify({ error: 'Missing user_id or notification data' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Get user's push subscriptions
    const { data: subscriptions, error: subscriptionsError } = await supabaseClient
      .from('push_subscriptions')
      .select('*')
      .eq('user_id', user_id)
      .eq('is_active', true)

    if (subscriptionsError) {
      console.error('Error fetching subscriptions:', subscriptionsError)
      return new Response(
        JSON.stringify({ error: 'Failed to fetch push subscriptions' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    if (!subscriptions || subscriptions.length === 0) {
      return new Response(
        JSON.stringify({ message: 'No active push subscriptions found for user' }),
        { 
          status: 200, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // VAPID keys - these should be set as environment variables
    const vapidPublicKey = Deno.env.get('VAPID_PUBLIC_KEY')
    const vapidPrivateKey = Deno.env.get('VAPID_PRIVATE_KEY')
    const vapidSubject = Deno.env.get('VAPID_SUBJECT') || 'mailto:<EMAIL>'

    if (!vapidPublicKey || !vapidPrivateKey) {
      console.error('VAPID keys not configured')
      return new Response(
        JSON.stringify({ error: 'Push notification service not configured' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Send push notifications to all user's subscriptions
    const pushPromises = subscriptions.map(async (subscription: any) => {
      try {
        const pushSubscription: PushSubscription = {
          endpoint: subscription.endpoint,
          p256dh_key: subscription.p256dh_key,
          auth_key: subscription.auth_key
        }

        // Prepare notification payload
        const payload: NotificationPayload = {
          title: notification.title,
          body: notification.body,
          icon: notification.icon || '/icons/icon-192x192.png',
          badge: notification.badge || '/icons/logo.png',
          data: notification.data || {},
          actions: notification.actions || [
            { action: 'view', title: 'View Details' },
            { action: 'dismiss', title: 'Dismiss' }
          ],
          tag: notification.tag || `stayfu-${Date.now()}`,
          requireInteraction: notification.requireInteraction || notification.priority === 'urgent',
          priority: notification.priority || 'normal',
          action_url: notification.action_url,
          notification_id: notification.notification_id
        }

        // Send push notification using Web Push Protocol
        const result = await sendWebPushNotification(
          pushSubscription,
          payload,
          vapidPublicKey,
          vapidPrivateKey,
          vapidSubject
        )

        return { subscription_id: subscription.id, success: true, result }
      } catch (error) {
        console.error(`Failed to send push to subscription ${subscription.id}:`, error)
        
        // If subscription is invalid, mark it as inactive
        if (error.message?.includes('410') || error.message?.includes('invalid')) {
          await supabaseClient
            .from('push_subscriptions')
            .update({ is_active: false })
            .eq('id', subscription.id)
        }

        return { subscription_id: subscription.id, success: false, error: error.message }
      }
    })

    const results = await Promise.all(pushPromises)
    const successCount = results.filter(r => r.success).length
    const failureCount = results.filter(r => !r.success).length

    // Update notification status in database
    if (notification.notification_id) {
      const deliveryStatus = successCount > 0 ? 'sent' : 'failed'
      await supabaseClient
        .from('notifications')
        .update({ 
          delivery_status: deliveryStatus,
          sent_at: new Date().toISOString()
        })
        .eq('id', notification.notification_id)
    }

    return new Response(
      JSON.stringify({
        message: 'Push notifications processed',
        total_subscriptions: subscriptions.length,
        successful_sends: successCount,
        failed_sends: failureCount,
        results
      }),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('Error in send-push-notification function:', error)
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})

// Helper function to send web push notification
async function sendWebPushNotification(
  subscription: PushSubscription,
  payload: NotificationPayload,
  vapidPublicKey: string,
  vapidPrivateKey: string,
  vapidSubject: string
): Promise<any> {
  // This is a simplified implementation
  // In a real implementation, you would use a proper Web Push library
  // For now, we'll use the fetch API to send to the push service
  
  const payloadString = JSON.stringify(payload)
  
  // Extract push service URL from endpoint
  const url = new URL(subscription.endpoint)
  const pushService = url.origin
  
  // Create JWT token for VAPID authentication
  const vapidToken = await createVapidToken(vapidSubject, vapidPublicKey, vapidPrivateKey)
  
  const response = await fetch(subscription.endpoint, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/octet-stream',
      'Content-Length': payloadString.length.toString(),
      'Authorization': `vapid t=${vapidToken}, k=${vapidPublicKey}`,
      'Crypto-Key': `p256ecdsa=${vapidPublicKey}`,
      'Content-Encoding': 'aes128gcm'
    },
    body: payloadString
  })

  if (!response.ok) {
    throw new Error(`Push service responded with ${response.status}: ${response.statusText}`)
  }

  return { status: response.status, statusText: response.statusText }
}

// Helper function to create VAPID JWT token
async function createVapidToken(subject: string, publicKey: string, privateKey: string): Promise<string> {
  // This is a simplified implementation
  // In a real implementation, you would use a proper JWT library with ECDSA signing
  
  const header = {
    typ: 'JWT',
    alg: 'ES256'
  }
  
  const payload = {
    aud: 'https://fcm.googleapis.com',
    exp: Math.floor(Date.now() / 1000) + 12 * 60 * 60, // 12 hours
    sub: subject
  }
  
  const encodedHeader = btoa(JSON.stringify(header)).replace(/=/g, '').replace(/\+/g, '-').replace(/\//g, '_')
  const encodedPayload = btoa(JSON.stringify(payload)).replace(/=/g, '').replace(/\+/g, '-').replace(/\//g, '_')
  
  // For a real implementation, you would sign this with the private key
  // For now, we'll return a placeholder token
  return `${encodedHeader}.${encodedPayload}.signature`
}
