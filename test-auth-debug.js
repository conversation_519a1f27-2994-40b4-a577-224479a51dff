const { createClient } = require('@supabase/supabase-js');

// Test script to debug authentication issue
const SUPABASE_URL = 'https://pwaeknalhosfwuxkpaet.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB3YWVrbmFsaG9zZnd1eGtwYWV0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDEwMjk1MzMsImV4cCI6MjA1NjYwNTUzM30.buZMBA9gPIkvp3rIGJl-QeYIO2IXUC8ea_N-AvLJID4';

async function testAuth() {
  console.log('🧪 Testing authentication flow...');
  
  const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
  
  // Override the original function invocation to add debugging
  const originalInvoke = supabase.functions.invoke;
  supabase.functions.invoke = async function(funcName, options) {
    console.log(`📞 Calling function: ${funcName}`);
    console.log(`📋 Request body:`, JSON.stringify(options?.body, null, 2));
    
    const result = await originalInvoke.call(this, funcName, options);
    
    console.log(`📊 Function response:`, JSON.stringify(result, null, 2));
    return result;
  };
  
  try {
    // Test login with the provided credentials
    console.log('1. Attempting login...');
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'Newsig1!!!'
    });
    
    if (authError) {
      console.error('❌ Login failed:', authError.message);
      return;
    }
    
    console.log('✅ Login successful, user ID:', authData.user.id);
    
    // Test session
    console.log('2. Checking session...');
    const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError) {
      console.error('❌ Session error:', sessionError.message);
      return;
    }
    
    console.log('✅ Session valid:', sessionData.session.user.id);
    console.log('📋 Access token:', sessionData.session.access_token.substring(0, 50) + '...');
    
    // Test the RPC function directly first
    console.log('3. Testing get_low_stock_items RPC function...');
    const { data: rpcData, error: rpcError } = await supabase.rpc('get_low_stock_items', {
      user_id_param: authData.user.id
    });
    
    if (rpcError) {
      console.error('❌ RPC error:', rpcError.message);
      console.error('Full RPC error:', rpcError);
    } else {
      console.log('✅ RPC response:', rpcData);
    }

    // Test AI command processor
    console.log('4. Testing AI command processor...');
    const { data: aiData, error: aiError } = await supabase.functions.invoke('ai-command-processor', {
      body: {
        command: 'create a purchase order for all low stock items',
        originalCommand: 'create a purchase order for all low stock items',
        hasContext: false
      }
    });
    
    if (aiError) {
      console.error('❌ AI processor error:', aiError.message);
      console.error('Full error:', aiError);
      return;
    }
    
    console.log('✅ AI processor response:', aiData);
    
  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

testAuth();